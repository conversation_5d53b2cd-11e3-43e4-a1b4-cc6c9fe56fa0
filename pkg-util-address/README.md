# Flow Util-Address ESM Build

这是 Flow Util-Address 的 ESM (ECMAScript Modules) 构建版本，从 `fcl-js/packages/util-address` 编译而来。

## 📁 项目结构

```
pkg-util-address/
├── dist/           # 编译后的 ESM JavaScript 文件
├── types/          # TypeScript 类型声明文件
├── package.json    # 包配置和构建脚本
├── tsconfig.json   # TypeScript 编译配置
├── fix-version.js  # 版本号修复脚本
└── example.js      # 使用示例
```

## 🚀 构建命令

```bash
# 清理并构建 ESM 版本
npm run build:esm

# 仅清理输出目录
npm run clean
```

## 📦 构建过程

1. **TypeScript 编译**: 使用 `tsc` 将 TypeScript 源码编译为 ESM JavaScript
2. **导入路径修复**: 使用 `ts-add-js-extension` 自动添加 `.js` 扩展名
3. **版本号修复**: 检查并修复版本号（util 包通常不需要）
4. **类型声明**: 生成完整的 TypeScript 类型定义

## 💡 使用方法

### ES Modules 导入

```javascript
// 完整导入
import * as utilAddress from './dist/index.js';

// 按需导入（推荐）
import { sansPrefix, withPrefix, display } from './dist/index.js';

// 使用示例
const address = "0xc88ff43f1a87c679";

// 移除 0x 前缀
console.log(sansPrefix(address)); // "c88ff43f1a87c679"

// 添加 0x 前缀
console.log(withPrefix("c88ff43f1a87c679")); // "0xc88ff43f1a87c679"

// 格式化显示（等同于 withPrefix）
console.log(display("c88ff43f1a87c679")); // "0xc88ff43f1a87c679"
```

### 类型支持

```typescript
import { sansPrefix, withPrefix, display } from './dist/index.js';
import type { } from './types/index.d.ts';

// 函数重载支持
const addr1: string = withPrefix("abc123");        // "0xabc123"
const addr2: null = withPrefix(null);              // null
const addr3: string | null = withPrefix(someAddr); // "0x..." or null
```

## 🔧 API 文档

### `sansPrefix(address)`

移除地址的 `0x` 或 `Fx` 前缀。

**参数:**
- `address: string | null` - Flow 地址

**返回值:**
- `string | null` - 不带前缀的地址

**示例:**
```javascript
sansPrefix("0xABC123");  // "ABC123"
sansPrefix("FxABC123");  // "ABC123"
sansPrefix("ABC123");    // "ABC123"
sansPrefix(null);        // null
```

### `withPrefix(address)`

为地址添加 `0x` 前缀（如果尚未存在）。

**参数:**
- `address: string | null` - Flow 地址

**返回值:**
- `string | null` - 带 `0x` 前缀的地址

**示例:**
```javascript
withPrefix("ABC123");     // "0xABC123"
withPrefix("0xABC123");   // "0xABC123"
withPrefix(null);         // null
```

### `display(address)`

格式化地址用于显示（等同于 `withPrefix`）。

**参数:**
- `address: string | null` - Flow 地址

**返回值:**
- `string | null` - 格式化后的地址

**示例:**
```javascript
display("ABC123");     // "0xABC123"
display("0xABC123");   // "0xABC123"
display(null);         // null
```

## 🧪 功能特性

- ✅ **前缀处理**: 智能处理 `0x` 和 `Fx` 前缀
- ✅ **空值安全**: 正确处理 `null` 值
- ✅ **类型安全**: 完整的 TypeScript 类型支持
- ✅ **函数重载**: 支持不同参数类型的函数重载
- ✅ **ESM 兼容**: 纯 ES Modules 格式

## ✅ 验证

运行示例文件来验证构建是否正常工作：

```bash
node example.js
```

## 🛠️ 自定义构建

如果需要修改构建配置：

1. 编辑 `tsconfig.json` 调整 TypeScript 编译选项
2. 更新 `package.json` 中的构建脚本

## 📝 注意事项

- 确保 Node.js 版本支持 ES Modules (>= 14.x)
- 在使用时需要明确指定 `.js` 扩展名
- 类型声明文件位于 `types/` 目录中
- 这是一个纯工具包，没有外部依赖

## 🎯 使用场景

这个工具包适用于：

- Flow 区块链地址格式化
- 前端应用中的地址显示
- 地址验证和标准化
- Flow DApp 开发

## 📊 性能

- **包大小**: 极小（< 1KB）
- **运行时**: 零依赖
- **兼容性**: 支持所有现代 JavaScript 环境
