{"include": ["../packages/util-address/src/**/*"], "exclude": ["../packages/util-address/src/**/*.test.ts", "../packages/util-address/src/**/*.test.js"], "compilerOptions": {"rootDir": "../packages/util-address/src", "outDir": "./dist", "declarationDir": "./types", "module": "ESNext", "target": "ES2020", "moduleResolution": "Node", "declaration": true, "emitDeclarationOnly": false, "allowJs": true, "strict": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "lib": ["ES2020", "WebWorker", "DOM", "ES2022.E<PERSON>r"], "types": ["node"], "baseUrl": "."}}