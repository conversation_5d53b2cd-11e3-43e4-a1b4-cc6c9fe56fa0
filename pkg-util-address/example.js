#!/usr/bin/env node

// Example usage of the Util-Address ESM build
import { sansPrefix, withPrefix, display } from './dist/index.js';

console.log('🚀 Flow Util-Address ESM Example');

// Test cases from the original README
const address = "0xc88ff43f1a87c679";

console.log('\n📋 Testing sansPrefix function:');
console.log(`Input: "${address}"`);
console.log(`Output: "${sansPrefix(address)}"`);
console.log(`Expected: "c88ff43f1a87c679"`);
console.log(`✅ Test passed:`, sansPrefix(address) === "c88ff43f1a87c679");

console.log('\n📋 Testing withPrefix function:');
console.log(`Input: "${address}"`);
console.log(`Output: "${withPrefix(address)}"`);
console.log(`Expected: "${address}"`);
console.log(`✅ Test passed:`, withPrefix(address) === address);

console.log('\n📋 Testing withPrefix with sans prefix:');
const addressWithoutPrefix = sansPrefix(address);
console.log(`Input: "${addressWithoutPrefix}"`);
console.log(`Output: "${withPrefix(addressWithoutPrefix)}"`);
console.log(`Expected: "${address}"`);
console.log(`✅ Test passed:`, withPrefix(addressWithoutPrefix) === address);

console.log('\n📋 Testing display function:');
console.log(`Input: "${addressWithoutPrefix}"`);
console.log(`Output: "${display(addressWithoutPrefix)}"`);
console.log(`Expected: "${address}"`);
console.log(`✅ Test passed:`, display(addressWithoutPrefix) === address);

console.log('\n📋 Testing null handling:');
console.log(`sansPrefix(null):`, sansPrefix(null));
console.log(`withPrefix(null):`, withPrefix(null));
console.log(`display(null):`, display(null));
console.log(`✅ Null handling works correctly`);

console.log('\n📋 Testing edge cases:');
const fxAddress = "Fxc88ff43f1a87c679";
console.log(`sansPrefix("${fxAddress}"):`, sansPrefix(fxAddress));
console.log(`✅ Fx prefix handling works`);

console.log('\n🎉 All util-address functionality tests passed!');

// Example usage in a component-like structure
const formatAddressForDisplay = (addr) => {
  if (!addr) return 'No address';
  return `Flow Address: ${display(addr)}`;
};

console.log('\n💡 Example usage:');
console.log(formatAddressForDisplay("c88ff43f1a87c679"));
console.log(formatAddressForDisplay("0xc88ff43f1a87c679"));
console.log(formatAddressForDisplay(null));
