{"name": "@onflow/util-address-esm", "version": "1.2.3", "description": "Flow JS SDK Util -- Address ESM Build", "type": "module", "main": "./dist/index.js", "types": "./types/index.d.ts", "exports": {".": {"types": "./types/index.d.ts", "import": "./dist/index.js"}}, "scripts": {"build:esm": "tsc -p ./tsconfig.json && ts-add-js-extension add --dir=dist && node fix-version.js", "clean": "rm -rf dist types", "prebuild:esm": "npm run clean"}, "dependencies": {"@babel/runtime": "^7.25.7"}, "devDependencies": {"typescript": "^5.6.3", "ts-add-js-extension": "^1.6.4", "@types/node": "^20.0.0"}}