// Type declarations for missing modules
declare module '@onflow/fcl-wc' {
  export function init(config: any): Promise<any>;
  export function initLazy(config: any): Promise<any>;
  export function connect(): Promise<any>;
  export function disconnect(): Promise<any>;
  export function isConnected(): boolean;
  export function getSession(): any;
  export function request(params: any): Promise<any>;
  export function createSessionProposal(params: any): any;
  export function getProvider(): any;
  export const events: any;
  export const FLOW_METHODS: any;
  export const SERVICE_PLUGIN_NAME: string;
}

declare module '@walletconnect/types' {
  export namespace CoreTypes {
    export interface Metadata {
      name: string;
      description: string;
      url: string;
      icons: string[];
    }
  }
}
