{"name": "@onflow/fcl-esm", "version": "1.18.0", "description": "Flow Client Library ESM Build", "type": "module", "main": "./dist/fcl.js", "types": "./types/fcl.d.ts", "exports": {".": {"types": "./types/fcl.d.ts", "import": "./dist/fcl.js"}}, "scripts": {"build:esm": "tsc -p ./tsconfig.json && ts-add-js-extension add --dir=dist && node fix-version.js", "clean": "rm -rf dist types", "prebuild:esm": "npm run clean"}, "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/config": "1.5.2", "@onflow/fcl-core": "1.19.0", "@onflow/fcl-wc": "6.0.3", "@onflow/interaction": "0.0.11", "@onflow/rlp": "1.2.3", "@onflow/sdk": "1.9.0", "@onflow/types": "1.4.1", "@onflow/util-actor": "1.3.4", "@onflow/util-address": "1.2.3", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3", "@onflow/util-rpc": "0.0.2", "@onflow/util-semver": "1.0.3", "@onflow/util-template": "1.2.3", "@onflow/util-uid": "1.2.3", "@walletconnect/types": "^2.13.2", "abort-controller": "^3.0.0", "cross-fetch": "^4.0.0", "events": "^3.3.0", "sha3": "^2.1.4"}, "devDependencies": {"typescript": "^5.6.3", "ts-add-js-extension": "^1.6.4", "@types/node": "^20.0.0"}}