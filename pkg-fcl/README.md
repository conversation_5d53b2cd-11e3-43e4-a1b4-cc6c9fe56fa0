# Flow Client Library (FCL) ESM Build

这是 Flow Client Library (FCL) 的 ESM (ECMAScript Modules) 构建版本，从 `fcl-js/packages/fcl` 编译而来。

## 📁 项目结构

```
pkg-fcl/
├── dist/           # 编译后的 ESM JavaScript 文件
├── types/          # TypeScript 类型声明文件
├── package.json    # 包配置和构建脚本
├── tsconfig.json   # TypeScript 编译配置
├── types.d.ts      # 自定义类型声明
├── fix-version.js  # 版本号修复脚本
└── example.js      # 使用示例
```

## 🚀 构建命令

```bash
# 清理并构建 ESM 版本
npm run build:esm

# 仅清理输出目录
npm run clean
```

## 📦 构建过程

1. **TypeScript 编译**: 使用 `tsc` 将 TypeScript 源码编译为 ESM JavaScript
2. **导入路径修复**: 使用 `ts-add-js-extension` 自动添加 `.js` 扩展名
3. **版本号修复**: 自动设置正确的版本号
4. **类型声明**: 生成完整的 TypeScript 类型定义

### 使用 ts-add-js-extension 的优势

- ✅ **专业工具**: 专门为 TypeScript ESM 导入问题设计
- ✅ **可靠性高**: 处理各种边缘情况和复杂的导入模式
- ✅ **社区维护**: 由开源社区维护，持续更新
- ✅ **功能完整**: 支持相对导入、绝对导入、目录导入等

## 💡 使用方法

### ES Modules 导入

```javascript
// 完整导入
import * as fcl from './dist/fcl.js';

// 按需导入
import { config, authenticate, query, mutate, script, transaction } from './dist/fcl.js';

// 使用示例
fcl.config({
  "accessNode.api": "https://rest-testnet.onflow.org",
  "discovery.wallet": "https://fcl-discovery.onflow.org/testnet/authn"
});

// 执行脚本
const result = await fcl.query({
  cadence: `
    pub fun main(): String {
      return "Hello Flow!"
    }
  `
});
```

### 类型支持

```typescript
import * as fcl from './dist/fcl.js';
import type { TransactionStatus } from './types/fcl.d.ts';

const txId = await fcl.mutate({
  cadence: `
    transaction {
      execute {
        log("Hello Flow!")
      }
    }
  `
});

const status: TransactionStatus = await fcl.tx(txId).onceSealed();
```

## 🔧 技术细节

- **模块格式**: ESNext
- **目标版本**: ES2020
- **模块解析**: Node.js
- **类型声明**: 包含完整的 TypeScript 类型定义

## 📋 依赖项

所有必要的 @onflow 依赖项都已包含在 package.json 中：

- @onflow/config
- @onflow/fcl-core
- @onflow/fcl-wc
- @onflow/interaction
- @onflow/rlp
- @onflow/sdk
- @onflow/types
- @onflow/util-* 系列工具包

## ✅ 验证

运行示例文件来验证构建是否正常工作：

```bash
node example.js
```

## 🛠️ 自定义构建

如果需要修改构建配置：

1. 编辑 `tsconfig.json` 调整 TypeScript 编译选项
2. 修改 `types.d.ts` 调整自定义类型声明
3. 更新 `package.json` 中的构建脚本

## 📝 注意事项

- 确保 Node.js 版本支持 ES Modules (>= 14.x)
- 在使用时需要明确指定 `.js` 扩展名
- 类型声明文件位于 `types/` 目录中
- 包含了对 @onflow/fcl-wc 和 @walletconnect/types 的自定义类型声明

## 🎯 功能特性

FCL ESM 构建包含了完整的 Flow Client Library 功能：

- ✅ **配置管理**: `fcl.config()`
- ✅ **用户认证**: `fcl.authenticate()`, `fcl.currentUser`
- ✅ **脚本查询**: `fcl.query()`, `fcl.script`
- ✅ **交易执行**: `fcl.mutate()`, `fcl.transaction`
- ✅ **账户信息**: `fcl.account()`
- ✅ **类型系统**: `fcl.t.*`
- ✅ **参数构建**: `fcl.arg()`, `fcl.args()`
- ✅ **工具函数**: `fcl.WalletUtils`, `fcl.AppUtils`
