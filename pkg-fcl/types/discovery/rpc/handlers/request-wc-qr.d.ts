import { DiscoveryRpc } from "../requests";
export declare const wcRequestHandlerFactory: ({ rpc, onExecResult, authnBody, abortSignal, }: {
    rpc: DiscoveryRpc;
    onExecResult: (result: any) => void;
    authnBody: any;
    abortSignal: AbortSignal;
}) => ({}: {}) => Promise<{
    uri: any;
}>;
export declare function watchQrFactory({ rpc, authnBody, }: {
    rpc: DiscoveryRpc;
    authnBody: any;
}): ({ uri, approval, onExecResult, }: {
    uri: string;
    onExecResult: (result: any) => void;
    approval: any;
}) => void;
