export declare function createDiscoveryRpcClient({ onExecResult, body, opts, args, abortSignal, }: {
    onExecResult: (result: any) => void;
    body: any;
    opts: any;
    args: any;
    abortSignal: AbortSignal;
}): {
    connect: ({ send }: {
        send: (msg: import("@onflow/util-rpc/types/messages").RpcMessage) => void;
    }) => void;
    receive: (msg: import("@onflow/util-rpc/types/messages").RpcMessage) => void;
};
