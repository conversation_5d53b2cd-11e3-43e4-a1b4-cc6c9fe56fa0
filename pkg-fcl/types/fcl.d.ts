export { VERSION, query, verifyUserSignatures, serialize, tx, events, pluginRegistry, discovery, t, WalletUtils, AppUtils, InteractionTemplateUtils, getChainId, TestUtils, config, flowMainnet, flowTestnet, flowEmulator, send, decode, account, block, isOk, isBad, why, pipe, build, withPrefix, sansPrefix, display, cadence, cdc, createSignableVoucher, voucherIntercept, voucherToTxId, transaction, script, ping, atBlockHeight, atBlockId, getAccount, getEvents, getEventsAtBlockHeightRange, getEventsAtBlockIds, getBlock, getBlockHeader, getCollection, getTransactionStatus, getTransaction, getNetworkParameters, getNodeVersionInfo, authorizations, authorization, args, arg, proposer, payer, limit, ref, params, param, validator, invariant, subscribeEvents, nodeVersionInfo, TransactionError, } from "@onflow/fcl-core";
export declare const currentUser: (() => {
    authenticate: ({ service, redir, forceReauth }?: {
        service?: object | undefined;
        redir?: boolean | undefined;
        forceReauth?: boolean | undefined;
    }) => Promise<any>;
    unauthenticate: () => void;
    authorization: (account: object) => Promise<object>;
    signUserMessage: (msg: string) => Promise<import("@onflow/fcl-core/types/current-user").CompositeSignature[]>;
    subscribe: (callback: Function) => Function;
    snapshot: () => Promise<import("@onflow/fcl-core/types/current-user").CurrentUser>;
    resolveArgument: () => Promise<import("@onflow/sdk/types/sdk").CadenceArgument<import("@onflow/types/types/types").TypeDescriptor<string, {
        type: string;
        value: string;
    }>>>;
}) & {
    authenticate: ({ service, redir, forceReauth }?: {
        service?: object | undefined;
        redir?: boolean | undefined;
        forceReauth?: boolean | undefined;
    }) => Promise<any>;
    unauthenticate: () => void;
    authorization: (account: object) => Promise<object>;
    signUserMessage: (msg: string) => Promise<import("@onflow/fcl-core/types/current-user").CompositeSignature[]>;
    subscribe: (callback: Function) => Function;
    snapshot: () => Promise<import("@onflow/fcl-core/types/current-user").CurrentUser>;
    resolveArgument: () => Promise<import("@onflow/sdk/types/sdk").CadenceArgument<import("@onflow/types/types/types").TypeDescriptor<string, {
        type: string;
        value: string;
    }>>>;
};
export declare const mutate: (opts?: {
    cadence?: string | undefined;
    args?: import("@onflow/fcl-core/types/exec/args").ArgsFn | undefined;
    template?: string | object | undefined;
    limit?: number | undefined;
    authz?: Function | undefined;
    proposer?: Function | undefined;
    payer?: Function | undefined;
    authorizations?: Function[] | undefined;
}) => Promise<string>;
export declare const authenticate: (opts?: {}) => Promise<any>;
export declare const unauthenticate: () => void;
export declare const reauthenticate: (opts?: {}) => Promise<any>;
export declare const signUp: (opts?: {}) => Promise<any>;
export declare const logIn: (opts?: {}) => Promise<any>;
export declare const authz: (account: object) => Promise<object>;
export { LOCAL_STORAGE, SESSION_STORAGE } from "./utils/web";
export { subscribe, subscribeRaw } from "@onflow/fcl-core";
