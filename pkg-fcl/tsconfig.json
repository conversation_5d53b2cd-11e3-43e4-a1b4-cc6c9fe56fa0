{"include": ["../packages/fcl/src/**/*", "./types.d.ts"], "exclude": ["../packages/fcl/src/**/*.test.ts", "../packages/fcl/src/**/*.test.js"], "compilerOptions": {"rootDir": "../packages/fcl/src", "outDir": "./dist", "declarationDir": "./types", "module": "ESNext", "target": "ES2020", "moduleResolution": "Node", "declaration": true, "emitDeclarationOnly": false, "allowJs": true, "strict": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "lib": ["ES2020", "WebWorker", "DOM", "ES2022.E<PERSON>r"], "types": ["node"], "baseUrl": ".", "paths": {"@onflow/*": ["./node_modules/@onflow/*"]}}}