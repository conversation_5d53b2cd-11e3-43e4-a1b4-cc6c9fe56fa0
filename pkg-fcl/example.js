#!/usr/bin/env node

// Example usage of the FCL ESM build
import * as fcl from './dist/fcl.js';

console.log('🚀 Flow Client Library ESM Example');
console.log('📦 FCL Version:', fcl.VERSION);

// Test basic functionality
try {
  // Test configuration
  fcl.config({
    "accessNode.api": "https://rest-testnet.onflow.org",
    "discovery.wallet": "https://fcl-discovery.onflow.org/testnet/authn"
  });
  
  console.log('✅ Configuration works');
  
  // Test script building
  const script = fcl.script`
    pub fun main(): String {
      return "Hello from Flow FCL!"
    }
  `;
  
  console.log('✅ Script builder works');
  
  // Test argument building
  const args = fcl.args([
    fcl.arg("test", fcl.t.String)
  ]);
  
  console.log('✅ Argument builder works');
  
  // Test account function
  console.log('✅ Account function available:', typeof fcl.account === 'function');
  
  // Test transaction function
  console.log('✅ Transaction function available:', typeof fcl.transaction === 'function');
  
  // Test authentication functions
  console.log('✅ Authentication functions available:', {
    authenticate: typeof fcl.authenticate === 'function',
    unauthenticate: typeof fcl.unauthenticate === 'function',
    currentUser: typeof fcl.currentUser === 'object'
  });
  
  console.log('🎉 All basic FCL functionality tests passed!');
  
} catch (error) {
  console.error('❌ Error:', error.message);
}
