#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixImportsInFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const fileDir = path.dirname(filePath);

  // Fix relative imports to include .js extension or /index.js
  const fixedContent = content.replace(
    /from\s+["'](\.[^"']*?)["']/g,
    (match, importPath) => {
      // Skip if already has extension
      if (path.extname(importPath)) {
        return match;
      }

      // Resolve the full path to check if it's a directory
      const fullPath = path.resolve(fileDir, importPath);
      const jsPath = fullPath + '.js';
      const indexPath = path.join(fullPath, 'index.js');

      // Check if it's a directory with index.js
      if (fs.existsSync(indexPath)) {
        return match.replace(importPath, importPath + '/index.js');
      }
      // Otherwise add .js extension
      return match.replace(importPath, importPath + '.js');
    }
  ).replace(
    /import\s+["'](\.[^"']*?)["']/g,
    (match, importPath) => {
      // Skip if already has extension
      if (path.extname(importPath)) {
        return match;
      }

      // Resolve the full path to check if it's a directory
      const fullPath = path.resolve(fileDir, importPath);
      const jsPath = fullPath + '.js';
      const indexPath = path.join(fullPath, 'index.js');

      // Check if it's a directory with index.js
      if (fs.existsSync(indexPath)) {
        return match.replace(importPath, importPath + '/index.js');
      }
      // Otherwise add .js extension
      return match.replace(importPath, importPath + '.js');
    }
  );

  if (content !== fixedContent) {
    fs.writeFileSync(filePath, fixedContent);
    console.log(`Fixed imports in: ${filePath}`);
  }
}

function walkDirectory(dir) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      walkDirectory(filePath);
    } else if (file.endsWith('.js')) {
      fixImportsInFile(filePath);
    }
  }
}

const distDir = path.join(__dirname, 'dist');
if (fs.existsSync(distDir)) {
  console.log('Fixing ESM imports...');
  walkDirectory(distDir);

  // Fix VERSION.js file
  const versionFile = path.join(distDir, 'VERSION.js');
  if (fs.existsSync(versionFile)) {
    const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
    const versionContent = `export const VERSION = "${packageJson.version}";\n`;
    fs.writeFileSync(versionFile, versionContent);
    console.log('Fixed VERSION.js');
  }

  console.log('Done!');
} else {
  console.log('dist directory not found');
}
