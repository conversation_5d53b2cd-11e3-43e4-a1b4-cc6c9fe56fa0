{"name": "@onflow/sdk-esm", "version": "1.9.0", "description": "Flow SDK ESM Build", "type": "module", "main": "./dist/sdk.js", "types": "./types/sdk.d.ts", "exports": {".": {"types": "./types/sdk.d.ts", "import": "./dist/sdk.js"}}, "scripts": {"build:esm": "tsc -p ./tsconfig.json && node fix-imports.js", "clean": "rm -rf dist types", "prebuild:esm": "npm run clean"}, "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/config": "1.5.2", "@onflow/rlp": "1.2.3", "@onflow/transport-http": "1.13.0", "@onflow/typedefs": "1.6.0", "@onflow/types": "1.4.1", "@onflow/util-actor": "1.3.4", "@onflow/util-address": "1.2.3", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3", "@onflow/util-template": "1.2.3", "deepmerge": "^4.3.1", "events": "^3.3.0", "sha3": "^2.1.4", "uuid": "^9.0.1"}, "devDependencies": {"typescript": "^5.6.3"}}