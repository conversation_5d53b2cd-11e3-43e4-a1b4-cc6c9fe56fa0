{"name": "@onflow/sdk-esm", "version": "1.9.0", "description": "Flow SDK ESM Build", "type": "module", "main": "./dist/sdk.js", "types": "./types/sdk.d.ts", "exports": {".": {"types": "./types/sdk.d.ts", "import": "./dist/sdk.js"}}, "scripts": {"build:esm": "rm -rf ./es && tsc -p ./tsconfig.json && ts-add-js-extension add --dir=es && node fix-version.js", "build:treeshaking": "rm -rf ./bundle.js && rollup -c"}, "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/config": "1.5.2", "@onflow/rlp": "1.2.3", "@onflow/transport-http": "1.13.0", "@onflow/typedefs": "1.6.0", "@onflow/types": "1.4.1", "@onflow/util-actor": "1.3.4", "@onflow/util-address": "1.2.3", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3", "@onflow/util-template": "1.2.3", "deepmerge": "^4.3.1", "events": "^3.3.0", "sha3": "^2.1.4", "uuid": "^9.0.1", "rollup": "4.41.1", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-swc": "0.4.0", "@rollup/plugin-terser": "0.4.4", "rollup-plugin-cleanup": "3.2.1", "@rollup/plugin-replace": "6.0.2", "rollup-plugin-visualizer": "6.0.1", "@rollup/plugin-commonjs": "28.0.3", "rollup-plugin-node-externals": "8.0.0", "@rollup/plugin-node-resolve": "16.0.1", "rollup-plugin-tslib-resolve-id": "0.0.0"}, "devDependencies": {"typescript": "^5.6.3", "ts-add-js-extension": "^1.6.4"}}