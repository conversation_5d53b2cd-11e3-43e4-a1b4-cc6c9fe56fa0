var ixModule, ROLES;
import { encode, Buffer } from "@onflow/rlp";
import { invariant } from "@onflow/util-invariant";
import { v4 } from "uuid";
import * as logger from "@onflow/util-logger";
import { log, LEVELS } from "@onflow/util-logger";
import { InteractionTag, InteractionStatus, InteractionResolverKind, TransactionRole } from "@onflow/typedefs";
import { config } from "@onflow/config";
import { send as send$1, httpTransport } from "@onflow/transport-http";
import { sansPrefix, withPrefix } from "@onflow/util-address";
import EventEmitter from "node:events";
const ACCT = `{\n  "kind":"${InteractionResolverKind.ACCOUNT}",\n  "tempId":null,\n  "addr":null,\n  "keyId":null,\n  "sequenceNum":null,\n  "signature":null,\n  "signingFunction":null,\n  "resolve":null,\n  "role": {\n    "proposer":false,\n    "authorizer":false,\n    "payer":false,\n    "param":false\n  }\n}`, ARG = `{\n  "kind":"${InteractionResolverKind.ARGUMENT}",\n  "tempId":null,\n  "value":null,\n  "asArgument":null,\n  "xform":null,\n  "resolve": null,\n  "resolveArgument": null\n}`, IX = `{\n  "tag":"${InteractionTag.UNKNOWN}",\n  "assigns":{},\n  "status":"${InteractionStatus.OK}",\n  "reason":null,\n  "accounts":{},\n  "params":{},\n  "arguments":{},\n  "message": {\n    "cadence":null,\n    "refBlock":null,\n    "computeLimit":null,\n    "proposer":null,\n    "payer":null,\n    "authorizations":[],\n    "params":[],\n    "arguments":[]\n  },\n  "proposer":null,\n  "authorizations":[],\n  "payer":[],\n  "events": {\n    "eventType":null,\n    "start":null,\n    "end":null,\n    "blockIds":[]\n  },\n  "subscribeEvents": {\n    "startBlockId":null,\n    "startHeight":null,\n    "eventTypes":null,\n    "addresses":null,\n    "contracts":null,\n    "heartbeatInterval":null\n  },\n  "transaction": {\n    "id":null\n  },\n  "block": {\n    "id":null,\n    "height":null,\n    "isSealed":null\n  },\n  "account": {\n    "addr":null\n  },\n  "collection": {\n    "id":null\n  }\n}`, KEYS = new Set(Object.keys(JSON.parse(IX))), initInteraction = () => JSON.parse(IX), isNumber$1 = d => "number" == typeof d, isArray$1 = d => Array.isArray(d), isObj = d => null !== d && "object" == typeof d, isNull = d => null == d, isFn$3 = d => "function" == typeof d, isInteraction = ix => {
  if (!isObj(ix) || isNull(ix) || isNumber$1(ix)) return !1;
  for (let key of KEYS) if (!ix.hasOwnProperty(key)) return !1;
  return !0;
}, Ok = ix => (ix.status = InteractionStatus.OK, ix), Bad = (ix, reason) => (ix.status = InteractionStatus.BAD, 
ix.reason = reason, ix), makeIx = wat => ix => (ix.tag = wat, Ok(ix)), prepAccountKeyId = acct => null == acct.keyId ? acct : (invariant(!isNaN(parseInt(acct.keyId.toString())), "account.keyId must be an integer"), 
{
  ...acct,
  keyId: parseInt(acct.keyId.toString())
}), initAccount = () => JSON.parse(ACCT), makeUnknown = makeIx(InteractionTag.UNKNOWN), makeScript = makeIx(InteractionTag.SCRIPT), makeTransaction = makeIx(InteractionTag.TRANSACTION), makeGetTransactionStatus = makeIx(InteractionTag.GET_TRANSACTION_STATUS), makeGetTransaction = makeIx(InteractionTag.GET_TRANSACTION), makeGetAccount = makeIx(InteractionTag.GET_ACCOUNT), makeGetEvents = makeIx(InteractionTag.GET_EVENTS), makePing = makeIx(InteractionTag.PING), makeGetBlock = makeIx(InteractionTag.GET_BLOCK), makeGetBlockHeader = makeIx(InteractionTag.GET_BLOCK_HEADER), makeGetCollection = makeIx(InteractionTag.GET_COLLECTION), makeGetNetworkParameters = makeIx(InteractionTag.GET_NETWORK_PARAMETERS), makeSubscribeEvents = makeIx(InteractionTag.SUBSCRIBE_EVENTS), makeGetNodeVerionInfo = makeIx(InteractionTag.GET_NODE_VERSION_INFO), is = wat => ix => ix.tag === wat, isUnknown = is(InteractionTag.UNKNOWN), isScript = is(InteractionTag.SCRIPT), isTransaction = is(InteractionTag.TRANSACTION), isGetTransactionStatus = is(InteractionTag.GET_TRANSACTION_STATUS), isGetTransaction = is(InteractionTag.GET_TRANSACTION), isGetAccount = is(InteractionTag.GET_ACCOUNT), isGetEvents = is(InteractionTag.GET_EVENTS), isPing = is(InteractionTag.PING), isGetBlock = is(InteractionTag.GET_BLOCK), isGetBlockHeader = is(InteractionTag.GET_BLOCK_HEADER), isGetCollection = is(InteractionTag.GET_COLLECTION), isGetNetworkParameters = is(InteractionTag.GET_NETWORK_PARAMETERS), isGetNodeVersionInfo = is(InteractionTag.GET_NODE_VERSION_INFO), isSubscribeEvents = is(InteractionTag.SUBSCRIBE_EVENTS), isBad = ix => ix.status === InteractionStatus.BAD, recPipe = async (ix, fns = []) => {
  try {
    if (ix = (ix => {
      for (let key of Object.keys(ix)) if (!KEYS.has(key)) throw new Error(`"${key}" is an invalid root level Interaction property.`);
      return ix;
    })(await ix), isBad(ix)) throw new Error(`Interaction Error: ${ix.reason}`);
    if (!fns.length) return ix;
    const [hd, ...rest] = fns, cur = await hd;
    if (isFn$3(cur)) return recPipe(cur(ix), rest);
    if (isNull(cur) || !cur) return recPipe(ix, rest);
    if (isInteraction(cur)) return recPipe(cur, rest);
    throw new Error("Invalid Interaction Composition");
  } catch (e) {
    throw e;
  }
};
function pipe(...args) {
  const [arg1, arg2] = args;
  if (isArray$1(arg1)) return d => pipe(d, arg1);
  return recPipe(arg1, arg2);
}
const identity = (v, ..._) => v, get = (ix, key, fallback = void 0) => null == ix.assigns[key] ? fallback : ix.assigns[key];
function build(fns = []) {
  return pipe(initInteraction(), fns);
}
ixModule = Object.freeze({
  __proto__: null,
  Bad: Bad,
  Ok: Ok,
  destroy: key => ix => (delete ix.assigns[key], Ok(ix)),
  get: get,
  initAccount: initAccount,
  initInteraction: initInteraction,
  interaction: () => (log.deprecate({
    pkg: "FCL/SDK",
    message: "The interaction been deprecated from the Flow JS-SDK/FCL. use initInteraction instead",
    transition: "https://github.com/onflow/flow-js-sdk/blob/master/packages/sdk/TRANSITIONS.md#0010-deprecate-interaction",
    level: LEVELS.warn
  }), initInteraction()),
  isAccount: account => account.kind === InteractionResolverKind.ACCOUNT,
  isArgument: argument => argument.kind === InteractionResolverKind.ARGUMENT,
  isArray: isArray$1,
  isBad: isBad,
  isFn: isFn$3,
  isGetAccount: isGetAccount,
  isGetBlock: isGetBlock,
  isGetBlockHeader: isGetBlockHeader,
  isGetCollection: isGetCollection,
  isGetEvents: isGetEvents,
  isGetNetworkParameters: isGetNetworkParameters,
  isGetNodeVersionInfo: isGetNodeVersionInfo,
  isGetTransaction: isGetTransaction,
  isGetTransactionStatus: isGetTransactionStatus,
  isInteraction: isInteraction,
  isNull: isNull,
  isNumber: isNumber$1,
  isObj: isObj,
  isOk: ix => ix.status === InteractionStatus.OK,
  isPing: isPing,
  isScript: isScript,
  isSubscribeEvents: isSubscribeEvents,
  isTransaction: isTransaction,
  isUnknown: isUnknown,
  makeArgument: arg => ix => {
    let tempId = v4();
    return ix.message.arguments.push(tempId), ix.arguments[tempId] = JSON.parse(ARG), 
    ix.arguments[tempId].tempId = tempId, ix.arguments[tempId].value = arg.value, ix.arguments[tempId].asArgument = arg.asArgument, 
    ix.arguments[tempId].xform = arg.xform, ix.arguments[tempId].resolve = arg.resolve, 
    ix.arguments[tempId].resolveArgument = isFn$3(arg.resolveArgument) ? arg.resolveArgument.bind(arg) : arg.resolveArgument, 
    Ok(ix);
  },
  makeGetAccount: makeGetAccount,
  makeGetBlock: makeGetBlock,
  makeGetBlockHeader: makeGetBlockHeader,
  makeGetCollection: makeGetCollection,
  makeGetEvents: makeGetEvents,
  makeGetNetworkParameters: makeGetNetworkParameters,
  makeGetNodeVerionInfo: makeGetNodeVerionInfo,
  makeGetTransaction: makeGetTransaction,
  makeGetTransactionStatus: makeGetTransactionStatus,
  makePing: makePing,
  makeScript: makeScript,
  makeSubscribeEvents: makeSubscribeEvents,
  makeTransaction: makeTransaction,
  makeUnknown: makeUnknown,
  pipe: pipe,
  prepAccount: (acct, opts = {}) => ix => {
    invariant("function" == typeof acct || "object" == typeof acct, "prepAccount must be passed an authorization function or an account object"), 
    invariant(null != opts.role, "Account must have a role");
    const ACCOUNT = initAccount(), role = opts.role, tempId = v4();
    let account = {
      ...acct
    };
    acct.authorization && isFn$3(acct.authorization) && (account = {
      resolve: acct.authorization
    }), !acct.authorization && isFn$3(acct) && (account = {
      resolve: acct
    });
    const resolve = account.resolve;
    return resolve && (account.resolve = (acct, ...rest) => [ resolve, prepAccountKeyId ].reduce(async (d, fn) => fn(await d, ...rest), acct)), 
    account = prepAccountKeyId(account), ix.accounts[tempId] = {
      ...ACCOUNT,
      tempId: tempId,
      ...account,
      role: {
        ...ACCOUNT.role,
        ..."object" == typeof acct.role ? acct.role : {},
        ...role ? {
          [role]: !0
        } : {}
      }
    }, role === TransactionRole.AUTHORIZER ? ix.authorizations.push(tempId) : role === TransactionRole.PAYER ? ix.payer.push(tempId) : role && (ix[role] = tempId), 
    ix;
  },
  put: (key, value) => ix => (ix.assigns[key] = value, Ok(ix)),
  update: (key, fn = identity) => ix => (ix.assigns[key] = fn(ix.assigns[key], ix), 
  Ok(ix)),
  why: ix => ix.reason
});
const DEFAULT_RESPONSE = {
  tag: null,
  transaction: null,
  transactionStatus: null,
  transactionId: null,
  encodedData: null,
  events: null,
  event: null,
  accountStatusEvent: null,
  account: null,
  block: null,
  blockHeader: null,
  blockDigest: null,
  latestBlock: null,
  collection: null,
  networkParameters: null,
  streamConnection: null,
  heartbeat: null,
  nodeVersionInfo: null
}, response = () => ({
  ...DEFAULT_RESPONSE
});
function getBlock(isSealed = null) {
  return pipe([ makeGetBlock, ix => (ix.block.isSealed = isSealed, Ok(ix)) ]);
}
const decodeStream = (stream, decodeResponse, customDecoders) => {
  const newStream = new EventEmitter;
  let queue = function() {
    let queue = [], running = !1;
    async function run() {
      if (!running) {
        for (running = !0; queue.length > 0; ) {
          const task = queue.shift();
          await (task?.());
        }
        running = !1;
      }
    }
    return {
      push: task => {
        queue.push(task), run();
      }
    };
  }();
  function relayEvent(event) {
    stream.on(event, message => {
      queue.push(async () => {
        newStream.emit(event, message);
      });
    });
  }
  return stream.on("data", async data => {
    const topics = Object.keys(data).filter(key => null != data[key] && "tag" !== key);
    let newDataPromise = Promise.all(topics.map(async channel => {
      const partialResponse = {
        [channel]: data[channel]
      };
      return {
        channel: channel,
        message: await decodeResponse(partialResponse, customDecoders)
      };
    }));
    queue.push(async () => {
      (await newDataPromise).forEach(({channel: channel, message: message}) => {
        newStream.emit(channel, message);
      });
    });
  }), relayEvent("close"), relayEvent("error"), {
    on(channel, callback) {
      return newStream.on(channel, callback), this;
    },
    off(channel, callback) {
      return newStream.off(channel, callback), this;
    },
    close: () => {
      stream.close();
    }
  };
};
const decodeImplicit = async i => i, decodeComposite = async (composite, decoders, stack) => {
  const decoded = await composite.fields.reduce(async (acc, v) => ((acc = await acc)[v.name] = await recurseDecode(v.value, decoders, [ ...stack, v.name ]), 
  acc), Promise.resolve({})), decoder = composite.id && decoderLookup(decoders, composite.id);
  return decoder ? await decoder(decoded) : decoded;
}, defaultDecoders = {
  UInt: decodeImplicit,
  Int: decodeImplicit,
  UInt8: decodeImplicit,
  Int8: decodeImplicit,
  UInt16: decodeImplicit,
  Int16: decodeImplicit,
  UInt32: decodeImplicit,
  Int32: decodeImplicit,
  UInt64: decodeImplicit,
  Int64: decodeImplicit,
  UInt128: decodeImplicit,
  Int128: decodeImplicit,
  UInt256: decodeImplicit,
  Int256: decodeImplicit,
  Word8: decodeImplicit,
  Word16: decodeImplicit,
  Word32: decodeImplicit,
  Word64: decodeImplicit,
  Word128: decodeImplicit,
  Word256: decodeImplicit,
  UFix64: decodeImplicit,
  Fix64: decodeImplicit,
  String: decodeImplicit,
  Character: decodeImplicit,
  Bool: decodeImplicit,
  Address: decodeImplicit,
  Void: async () => null,
  Optional: async (optional, decoders, stack) => optional ? await recurseDecode(optional, decoders, stack) : null,
  Reference: decodeImplicit,
  Array: async (array, decoders, stack) => await Promise.all(array.map(v => new Promise(async res => res(await recurseDecode(v, decoders, [ ...stack, v.type ]))))),
  Dictionary: async (dictionary, decoders, stack) => await dictionary.reduce(async (acc, v) => ((acc = await acc)[await recurseDecode(v.key, decoders, [ ...stack, v.key ])] = await recurseDecode(v.value, decoders, [ ...stack, v.key ]), 
  acc), Promise.resolve({})),
  Event: decodeComposite,
  Resource: decodeComposite,
  Struct: decodeComposite,
  Enum: decodeComposite,
  Type: async type => type.staticType,
  Path: decodeImplicit,
  Capability: decodeImplicit,
  InclusiveRange: async (range, decoders, stack) => {
    const keys = [ "start", "end", "step" ];
    return await Object.keys(range).reduce(async (acc, key) => (acc = await acc, keys.includes(key) && (acc[key] = await recurseDecode(range[key], decoders, [ ...stack, key ])), 
    acc), Promise.resolve({}));
  }
}, decoderLookup = (decoders, lookup) => {
  const found = Object.keys(decoders).find(decoder => {
    if (/^\/.*\/$/.test(decoder)) {
      return new RegExp(decoder.substring(1, decoder.length - 1)).test(lookup);
    }
    return decoder === lookup;
  });
  return lookup && found && decoders[found];
}, recurseDecode = async (decodeInstructions, decoders, stack) => {
  let decoder = decoderLookup(decoders, decodeInstructions.type);
  if (!decoder) throw new Error(`Undefined Decoder Error: ${decodeInstructions.type}@${stack.join(".")}`);
  return await decoder(decodeInstructions.value, decoders, stack);
}, decode = async (decodeInstructions, customDecoders = {}, stack = []) => {
  const filteredDecoders = Object.keys(defaultDecoders).filter(decoder => !Object.keys(customDecoders).find(customDecoder => new RegExp(customDecoder).test(decoder))).reduce((decoders, decoderKey) => (decoders[decoderKey] = defaultDecoders[decoderKey], 
  decoders), customDecoders), decoders = {
    ...filteredDecoders,
    ...customDecoders
  };
  return recurseDecode(decodeInstructions, decoders, stack);
}, decodeResponse = async (response, customDecoders = {}) => {
  if (response.encodedData) return decode(response.encodedData, customDecoders);
  if (response.transactionStatus) return {
    ...response.transactionStatus,
    events: await Promise.all(response.transactionStatus.events.map(async function(e) {
      return {
        type: e.type,
        transactionId: e.transactionId,
        transactionIndex: e.transactionIndex,
        eventIndex: e.eventIndex,
        data: await decode(e.payload, customDecoders)
      };
    }))
  };
  if (response.transaction) return response.transaction;
  if (response.events) return await Promise.all(response.events.map(async function(e) {
    return {
      blockId: e.blockId,
      blockHeight: e.blockHeight,
      blockTimestamp: e.blockTimestamp,
      type: e.type,
      transactionId: e.transactionId,
      transactionIndex: e.transactionIndex,
      eventIndex: e.eventIndex,
      data: await decode(e.payload, customDecoders)
    };
  }));
  if (response.event) {
    const {payload: payload, ...rest} = response.event;
    return {
      ...rest,
      data: await decode(payload, customDecoders)
    };
  }
  if (response.accountStatusEvent) {
    const {payload: payload, ...rest} = response.accountStatusEvent;
    return {
      ...rest,
      data: await decode(payload, customDecoders)
    };
  }
  if (response.account) return response.account;
  if (response.block) return response.block;
  if (response.blockHeader) return response.blockHeader;
  if (response.blockDigest) return response.blockDigest;
  if (response.latestBlock) return log.deprecate({
    pkg: "@onflow/decode",
    subject: "Operating upon data of the latestBlock field of the response object",
    transition: "https://github.com/onflow/flow-js-sdk/blob/master/packages/decode/WARNINGS.md#0001-Deprecating-latestBlock-field"
  }), response.latestBlock;
  if (response.transactionId) return response.transactionId;
  if (response.collection) return response.collection;
  if (response.networkParameters) {
    const prefixRegex = /^flow-/, rawChainId = response.networkParameters.chainId;
    let formattedChainId;
    return formattedChainId = "flow-emulator" === rawChainId ? "local" : prefixRegex.test(rawChainId) ? rawChainId.replace(prefixRegex, "") : rawChainId, 
    {
      chainId: formattedChainId
    };
  }
  return response.streamConnection ? decodeStream(response.streamConnection, decodeResponse, customDecoders) : response.heartbeat ? response.heartbeat : response.nodeVersionInfo ? response.nodeVersionInfo : null;
}, isFn$2 = v => "function" == typeof v, isString$1 = v => "string" == typeof v;
function isOldIdentifierSyntax(cadence) {
  return /\b(0x\w+)\b/g.test(cadence);
}
function isNewIdentifierSyntax(cadence) {
  return /import\s+"(\w+)"/g.test(cadence);
}
const isFn$1 = v => "function" == typeof v;
function cast(arg) {
  return invariant(null != typeof arg.xform, `No type specified for argument: ${arg.value}`), 
  isFn$1(arg.xform) ? arg.xform(arg.value) : isFn$1(arg.xform.asArgument) ? arg.xform.asArgument(arg.value) : void invariant(!1, "Invalid Argument", arg);
}
async function handleArgResolution(arg, depth = 3) {
  if (invariant(depth > 0, `Argument Resolve Recursion Limit Exceeded for Arg: ${arg.tempId}`), 
  isFn$1(arg.resolveArgument)) {
    return handleArgResolution(await arg.resolveArgument(), depth - 1);
  }
  return arg;
}
const leftPaddedHexBuffer = (value, pad) => Buffer.from(value.padStart(2 * pad, "0"), "hex"), TRANSACTION_DOMAIN_TAG = (value = Buffer.from("FLOW-V0.0-transaction").toString("hex"), 
pad = 32, Buffer.from(value.padEnd(2 * pad, "0"), "hex")).toString("hex");
var value, pad;
const prependTransactionDomainTag = tx => TRANSACTION_DOMAIN_TAG + tx, addressBuffer = addr => leftPaddedHexBuffer(addr, 8), argumentToString = arg => Buffer.from(JSON.stringify(arg), "utf8"), rlpEncode = v => encode(v).toString("hex"), preparePayload = tx => {
  return validatePayload(tx), [ (script = tx.cadence || "", Buffer.from(script, "utf8")), tx.arguments.map(argumentToString), (block = tx.refBlock || "", 
  leftPaddedHexBuffer(block, 32)), tx.computeLimit, addressBuffer(sansPrefix(tx.proposalKey.address || "")), tx.proposalKey.keyId, tx.proposalKey.sequenceNum, addressBuffer(sansPrefix(tx.payer)), tx.authorizers.map(authorizer => addressBuffer(sansPrefix(authorizer))) ];
  var block, script;
}, prepareEnvelope = tx => (validateEnvelope(tx), [ preparePayload(tx), preparePayloadSignatures(tx) ]), preparePayloadSignatures = tx => {
  const signers = collectSigners(tx);
  return tx.payloadSigs?.map(sig => ({
    signerIndex: signers.get(sansPrefix(sig.address)) || "",
    keyId: sig.keyId,
    sig: sig.sig
  })).sort((a, b) => a.signerIndex > b.signerIndex ? 1 : a.signerIndex < b.signerIndex ? -1 : a.keyId > b.keyId ? 1 : a.keyId < b.keyId ? -1 : 0).map(sig => {
    return [ sig.signerIndex, sig.keyId, (signature = sig.sig, Buffer.from(signature, "hex")) ];
    var signature;
  });
}, collectSigners = tx => {
  const signers = new Map;
  let i = 0;
  const addSigner = addr => {
    signers.has(addr) || (signers.set(addr, i), i++);
  };
  return tx.proposalKey.address && addSigner(tx.proposalKey.address), addSigner(tx.payer), 
  tx.authorizers.forEach(addSigner), signers;
}, validatePayload = tx => {
  payloadFields.forEach(field => checkField(tx, field)), proposalKeyFields.forEach(field => checkField(tx.proposalKey, field, "proposalKey"));
}, validateEnvelope = tx => {
  payloadSigsFields.forEach(field => checkField(tx, field)), tx.payloadSigs?.forEach((sig, index) => {
    payloadSigFields.forEach(field => checkField(sig, field, "payloadSigs", index));
  });
}, isNumber = v => "number" == typeof v, isString = v => "string" == typeof v, isObject = v => null !== v && "object" == typeof v, isArray = v => isObject(v) && v instanceof Array, payloadFields = [ {
  name: "cadence",
  check: isString
}, {
  name: "arguments",
  check: isArray
}, {
  name: "refBlock",
  check: isString,
  defaultVal: "0"
}, {
  name: "computeLimit",
  check: isNumber
}, {
  name: "proposalKey",
  check: isObject
}, {
  name: "payer",
  check: isString
}, {
  name: "authorizers",
  check: isArray
} ], proposalKeyFields = [ {
  name: "address",
  check: isString
}, {
  name: "keyId",
  check: isNumber
}, {
  name: "sequenceNum",
  check: isNumber
} ], payloadSigsFields = [ {
  name: "payloadSigs",
  check: isArray
} ], payloadSigFields = [ {
  name: "address",
  check: isString
}, {
  name: "keyId",
  check: isNumber
}, {
  name: "sig",
  check: isString
} ], checkField = (obj, field, base, index) => {
  const {name: name, check: check, defaultVal: defaultVal} = field;
  if (null == obj[name] && null != defaultVal && (obj[name] = defaultVal), null == obj[name]) throw missingFieldError(name, base, index);
  if (!check(obj[name])) throw invalidFieldError(name, base, index);
}, printFieldName = (field, base, index) => base ? null == index ? `${base}.${field}` : `${base}.${index}.${field}` : field, missingFieldError = (field, base, index) => new Error(`Missing field ${printFieldName(field, base, index)}`), invalidFieldError = (field, base, index) => new Error(`Invalid field ${printFieldName(field, base, index)}`);
function findInsideSigners(ix) {
  let inside = new Set(ix.authorizations);
  return ix.proposer && inside.add(ix.proposer), Array.isArray(ix.payer) ? ix.payer.forEach(p => inside.delete(p)) : inside.delete(ix.payer), 
  Array.from(inside);
}
function findOutsideSigners(ix) {
  let outside = new Set(Array.isArray(ix.payer) ? ix.payer : [ ix.payer ]);
  return Array.from(outside);
}
const createSignableVoucher = ix => {
  const proposalKey = ix.proposer ? {
    address: withPrefix(ix.accounts[ix.proposer].addr),
    keyId: ix.accounts[ix.proposer].keyId,
    sequenceNum: ix.accounts[ix.proposer].sequenceNum
  } : {};
  return {
    cadence: ix.message.cadence,
    refBlock: ix.message.refBlock || null,
    computeLimit: ix.message.computeLimit,
    arguments: ix.message.arguments.map(id => ix.arguments[id].asArgument),
    proposalKey: proposalKey,
    payer: withPrefix(ix.accounts[Array.isArray(ix.payer) ? ix.payer[0] : ix.payer].addr),
    authorizers: ix.authorizations.map(cid => withPrefix(ix.accounts[cid].addr)).reduce((prev, current) => prev.find(item => item === current) ? prev : [ ...prev, current ], []),
    payloadSigs: findInsideSigners(ix).map(id => ({
      address: withPrefix(ix.accounts[id].addr),
      keyId: ix.accounts[id].keyId,
      sig: ix.accounts[id].signature
    })),
    envelopeSigs: findOutsideSigners(ix).map(id => ({
      address: withPrefix(ix.accounts[id].addr),
      keyId: ix.accounts[id].keyId,
      sig: ix.accounts[id].signature
    }))
  };
};
function recurseFlatMap(el, depthLimit = 3) {
  return depthLimit <= 0 ? el : Array.isArray(el) ? recurseFlatMap(el.flatMap(e => e), depthLimit - 1) : el;
}
function addAccountToIx(ix, newAccount) {
  var acct;
  "string" != typeof newAccount.addr || "number" != typeof newAccount.keyId && "string" != typeof newAccount.keyId ? newAccount.tempId = newAccount.tempId || v4() : newAccount.tempId = `${withPrefix((acct = newAccount).addr)}-${acct.keyId}`;
  const existingAccount = ix.accounts[newAccount.tempId] || newAccount;
  return ix.accounts[newAccount.tempId] || (ix.accounts[newAccount.tempId] = newAccount), 
  ix.accounts[newAccount.tempId].role.proposer = existingAccount.role.proposer || newAccount.role.proposer, 
  ix.accounts[newAccount.tempId].role.payer = existingAccount.role.payer || newAccount.role.payer, 
  ix.accounts[newAccount.tempId].role.authorizer = existingAccount.role.authorizer || newAccount.role.authorizer, 
  ix.accounts[newAccount.tempId];
}
function uniqueAccountsFlatMap(accounts) {
  const flatMapped = recurseFlatMap(accounts), seen = new Set;
  return flatMapped.map(account => {
    const accountId = ((...ids) => ids.join("-"))(account.tempId, account.role.payer, account.role.proposer, account.role.authorizer, account.role.param);
    return seen.has(accountId) ? null : (seen.add(accountId), account);
  }).filter(e => null !== e);
}
async function resolveSingleAccount(ix, currentAccountTempId, depthLimit = 5, {debugLogger: debugLogger}) {
  if (depthLimit <= 0) throw new Error("recurseResolveAccount Error: Depth limit (5) reached. Ensure your authorization functions resolve to an account after 5 resolves.");
  let account = ix.accounts[currentAccountTempId];
  if (!account) return [ [], !1 ];
  if (debugLogger(`account: ${account.tempId}`, Math.max(5 - depthLimit, 0)), account?.resolve) {
    if (v = account?.resolve, v && ("[object Function]" === Object.prototype.toString.call(v) || "function" == typeof v || v instanceof Function)) {
      debugLogger(`account: ${account.tempId} -- cache MISS`, Math.max(5 - depthLimit, 0));
      const {resolve: resolve, ...accountWithoutResolve} = account;
      let resolvedAccounts = await resolve(accountWithoutResolve, function(acct, ix) {
        try {
          return {
            f_type: "PreSignable",
            f_vsn: "1.0.1",
            roles: acct.role,
            cadence: ix.message.cadence,
            args: ix.message.arguments.map(d => ix.arguments[d].asArgument),
            data: {},
            interaction: ix,
            voucher: createSignableVoucher(ix)
          };
        } catch (error) {
          throw console.error("buildPreSignable", error), error;
        }
      }(accountWithoutResolve, ix));
      resolvedAccounts = Array.isArray(resolvedAccounts) ? resolvedAccounts : [ resolvedAccounts ];
      let flatResolvedAccounts = recurseFlatMap(resolvedAccounts);
      return flatResolvedAccounts = flatResolvedAccounts.map(flatResolvedAccount => addAccountToIx(ix, flatResolvedAccount)), 
      account.resolve = flatResolvedAccounts.map(flatResolvedAccount => flatResolvedAccount.tempId), 
      account = addAccountToIx(ix, account), [ flatResolvedAccounts.map(flatResolvedAccount => flatResolvedAccount.tempId), !0 ];
    }
    return debugLogger(`account: ${account.tempId} -- cache HIT`, Math.max(5 - depthLimit, 0)), 
    [ account.resolve, !1 ];
  }
  var v;
  return [ account.tempId ? [ account.tempId ] : [], !1 ];
}
!function(ROLES) {
  ROLES.PAYER = "payer", ROLES.PROPOSER = "proposer", ROLES.AUTHORIZATIONS = "authorizations";
}(ROLES || (ROLES = {}));
const getAccountTempIDs = rawTempIds => null === rawTempIds ? [] : Array.isArray(rawTempIds) ? rawTempIds : [ rawTempIds ];
async function replaceRoles(ix, oldAccountTempId, newAccounts) {
  for (let role of Object.values(ROLES)) if (role === ROLES.AUTHORIZATIONS || role === ROLES.PAYER) ix[role] = getAccountTempIDs(ix[role]).reduce((acc, acctTempId) => acctTempId === oldAccountTempId ? acc.concat(...newAccounts.filter(x => role === ROLES.PAYER && x.role.payer || role === ROLES.AUTHORIZATIONS && x.role.authorizer).map(acct => acct.tempId)) : acc.concat(acctTempId), []); else if (role === ROLES.PROPOSER) {
    const proposerAccts = newAccounts.filter(x => x.role.proposer);
    if (proposerAccts.length > 1) throw new Error("replaceRoles Error: Multiple proposer keys were resolved, but only one is allowed");
    ix[role] = proposerAccts[0]?.tempId ?? ix[role];
  }
}
async function resolveAccountsByIds(ix, accountTempIds, depthLimit = 5, {debugLogger: debugLogger}) {
  invariant(ix && "object" == typeof ix, "resolveAccountType Error: ix not defined");
  let payerAddress, newTempIds = new Set;
  for (let accountId of accountTempIds) {
    let account = ix.accounts[accountId];
    invariant(Boolean(account), "resolveAccountType Error: account not found");
    const [resolvedAccountTempIds, foundNewAccounts] = await resolveSingleAccount(ix, accountId, depthLimit, {
      debugLogger: debugLogger
    });
    if (foundNewAccounts) {
      const flatResolvedAccounts = uniqueAccountsFlatMap(resolvedAccountTempIds.map(resolvedAccountTempId => ix.accounts[resolvedAccountTempId]));
      flatResolvedAccounts.forEach(x => newTempIds.add(x.tempId)), replaceRoles(ix, accountId, flatResolvedAccounts);
    }
  }
  for (const payerTempID of ix[ROLES.PAYER]) {
    let pAcct = ix.accounts[payerTempID];
    if (payerAddress) {
      if (payerAddress !== pAcct.addr) throw new Error("resolveAccountType Error: payers from different accounts detected");
    } else payerAddress = pAcct.addr;
  }
  return newTempIds;
}
function fetchSignature(ix, payload) {
  return async function(id) {
    const acct = ix.accounts[id];
    if (null != acct.signature && void 0 !== acct.signature) return;
    const {signature: signature} = await acct.signingFunction(function(acct, message, ix) {
      try {
        return {
          f_type: "Signable",
          f_vsn: "1.0.1",
          message: message,
          addr: sansPrefix(acct.addr),
          keyId: acct.keyId,
          roles: acct.role,
          cadence: ix.message.cadence,
          args: ix.message.arguments.map(d => ix.arguments[d].asArgument),
          data: {},
          interaction: ix,
          voucher: createSignableVoucher(ix)
        };
      } catch (error) {
        throw console.error("buildSignable", error), error;
      }
    }(acct, payload, ix));
    ix.accounts[id].signature = signature;
  };
}
function prepForEncoding(ix) {
  const payerAddress = sansPrefix((Array.isArray(ix.payer) ? ix.accounts[ix.payer[0]] : ix.accounts[ix.payer]).addr || ""), proposalKey = ix.proposer ? {
    address: sansPrefix(ix.accounts[ix.proposer].addr) || "",
    keyId: ix.accounts[ix.proposer].keyId || 0,
    sequenceNum: ix.accounts[ix.proposer].sequenceNum || 0
  } : {};
  return {
    cadence: ix.message.cadence,
    refBlock: ix.message.refBlock,
    computeLimit: ix.message.computeLimit,
    arguments: ix.message.arguments.map(id => ix.arguments[id].asArgument),
    proposalKey: proposalKey,
    payer: payerAddress,
    authorizers: ix.authorizations.map(cid => sansPrefix(ix.accounts[cid].addr) || "").reduce((prev, current) => prev.find(item => item === current) ? prev : [ ...prev, current ], [])
  };
}
const noop = v => v, debug = (key, fn = noop) => async ix => (await config.get(`debug.${key}`) && await fn(ix, (...msg) => {
  console.log(`debug[${key}] ---\n`, ...msg, "\n\n\n---");
}, ix => [ "\nAccounts:", {
  proposer: ix.proposer,
  authorizations: ix.authorizations,
  payer: ix.payer
}, "\n\nDetails:", ix.accounts ].filter(Boolean)), ix), resolve = pipe([ async function(ix) {
  if (!isTransaction(ix) && !isScript(ix)) return ix;
  var cadence = get(ix, "ix.cadence");
  if (invariant(isFn$2(cadence) || isString$1(cadence), "Cadence needs to be a function or a string."), 
  isFn$2(cadence) && (cadence = await cadence({})), invariant(isString$1(cadence), "Cadence needs to be a string at this point."), 
  invariant(!isOldIdentifierSyntax(cadence) || !isNewIdentifierSyntax(cadence), "Both account identifier and contract identifier syntax not simultaneously supported."), 
  isOldIdentifierSyntax(cadence) && (cadence = await config().where(/^0x/).then(d => Object.entries(d).reduce((cadence, [key, value]) => {
    const regex = new RegExp("(\\b" + key + "\\b)", "g");
    return cadence.replace(regex, value);
  }, cadence))), isNewIdentifierSyntax(cadence)) for (const [fullMatch, contractName] of function(cadence) {
    return cadence.matchAll(/import\s+"(\w+)"/g);
  }(cadence)) {
    const address = await config().get(`system.contracts.${contractName}`);
    address ? cadence = cadence.replace(fullMatch, `import ${contractName} from ${withPrefix(address)}`) : logger.log({
      title: "Contract Placeholder not found",
      message: `Cannot find a value for contract placeholder ${contractName}. Please add to your flow.json or explicitly add it to the config 'contracts.*' namespace.`,
      level: logger.LEVELS.warn
    });
  }
  return ix.message.cadence = cadence, ix;
}, debug("cadence", (ix, log) => log(ix.message.cadence)), async function(ix) {
  return isTransaction(ix) && (ix.message.computeLimit = ix.message.computeLimit || await config.get("fcl.limit"), 
  ix.message.computeLimit || (logger.log.deprecate({
    pkg: "FCL/SDK",
    subject: "The built-in default compute limit (DEFAULT_COMPUTE_LIMIT=10)",
    transition: "https://github.com/onflow/flow-js-sdk/blob/master/packages/sdk/TRANSITIONS.md#0009-deprecate-default-compute-limit"
  }), ix.message.computeLimit = 100)), ix;
}, debug("compute limit", (ix, log) => log(ix.message.computeLimit)), async function(ix) {
  if (isTransaction(ix) || isScript(ix)) for (let [id, arg] of Object.entries(ix.arguments)) {
    const res = await handleArgResolution(arg);
    ix.arguments[id].asArgument = cast(res);
  }
  return ix;
}, debug("arguments", (ix, log) => log(ix.message.arguments, ix.message)), async function(ix, opts = {}) {
  if (isTransaction(ix)) {
    Array.isArray(ix.payer) || log.deprecate({
      pkg: "FCL",
      subject: '"ix.payer" must be an array. Support for ix.payer as a singular',
      message: "See changelog for more info."
    });
    let [debugLogger, getDebugMessage] = function() {
      const DEBUG_MESSAGE = [];
      return [ function(msg = "", indent = 0) {
        DEBUG_MESSAGE.push(Array(4 * indent).fill(" ").join("-") + msg);
      }, function() {
        return DEBUG_MESSAGE.reduce((prev, curr) => prev + "\n" + curr);
      } ];
    }();
    try {
      let depthLimit = 5, frontier = new Set([ ...getAccountTempIDs(ix[ROLES.PAYER]), ...getAccountTempIDs(ix[ROLES.PROPOSER]), ...getAccountTempIDs(ix[ROLES.AUTHORIZATIONS]) ]);
      for (;frontier.size > 0; ) {
        if (depthLimit <= 0) throw new Error("resolveAccounts Error: Depth limit (5) reached. Ensure your authorization functions resolve to an account after 5 resolves.");
        frontier = await resolveAccountsByIds(ix, frontier, depthLimit, {
          debugLogger: debugLogger
        }), depthLimit--;
      }
      await async function(ix) {
        const payerTempIds = Array.isArray(ix.payer) ? ix.payer : [ ix.payer ], authorizersTempIds = Array.isArray(ix.authorizations) ? ix.authorizations : [ ix.authorizations ], proposerTempIds = null === ix.proposer ? [] : Array.isArray(ix.proposer) ? ix.proposer : [ ix.proposer ], ixAccountKeys = Object.keys(ix.accounts), uniqueTempIds = [ ...new Set(payerTempIds.concat(authorizersTempIds, proposerTempIds)) ];
        for (const ixAccountKey of ixAccountKeys) uniqueTempIds.find(id => id === ixAccountKey) || delete ix.accounts[ixAccountKey];
      }(ix);
      for (const role of Object.values(ROLES)) invariant(getAccountTempIDs(ix[role]).length > 0 || role === ROLES.AUTHORIZATIONS, `resolveAccountType Error: no accounts for role "${role}" found`);
      opts.enableDebug && console.debug(getDebugMessage());
    } catch (error) {
      throw console.error("=== SAD PANDA ===\n\n", error, "\n\n=== SAD PANDA ==="), error;
    }
  }
  return ix;
}, debug("accounts", (ix, log, accts) => log(...accts(ix))), async function(ix) {
  if (isTransaction(ix) && null == ix.message.refBlock) {
    const node = await config().get("accessNode.api"), sendFn = await config.first([ "sdk.transport", "sdk.send" ], send$1);
    invariant(sendFn, "Required value for sdk.transport is not defined in config. See: https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21"), 
    ix.message.refBlock = (await sendFn(build([ getBlock() ]), {
      config: config,
      response: response,
      Buffer: Buffer,
      ix: ixModule
    }, {
      node: node
    }).then(decodeResponse)).id;
  }
  return ix;
}, async function(ix) {
  if (isTransaction(ix)) {
    var acct = Object.values(ix.accounts).find(a => a.role.proposer);
    if (invariant(void 0 !== acct, "Transactions require a proposer"), acct && null == acct.sequenceNum) {
      const node = await config().get("accessNode.api"), sendFn = await config.first([ "sdk.transport", "sdk.send" ], send$1);
      invariant(sendFn, "Required value for sdk.transport is not defined in config. See: https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21"), 
      ix.accounts[acct.tempId].sequenceNum = await sendFn(await build([ (addr = acct.addr, 
      pipe([ makeGetAccount, ix => (ix.account.addr = sansPrefix(addr), Ok(ix)) ])) ]), {
        config: config,
        response: response,
        Buffer: Buffer,
        ix: ixModule
      }, {
        node: node
      }).then(decodeResponse).then(acctResponse => acctResponse.keys).then(keys => keys.find(key => key.index === acct.keyId)).then(key => key.sequenceNumber);
    }
  }
  var addr;
  return ix;
}, async function(ix) {
  if (isTransaction(ix)) try {
    let insideSigners = findInsideSigners(ix);
    const insidePayload = (tx = prepForEncoding(ix), prependTransactionDomainTag(rlpEncode(preparePayload(tx))));
    await Promise.all(insideSigners.map(fetchSignature(ix, insidePayload)));
    let outsideSigners = findOutsideSigners(ix);
    const outsidePayload = (tx => prependTransactionDomainTag(rlpEncode(prepareEnvelope(tx))))({
      ...prepForEncoding(ix),
      payloadSigs: insideSigners.map(id => ({
        address: ix.accounts[id].addr || "",
        keyId: ix.accounts[id].keyId || 0,
        sig: ix.accounts[id].signature || ""
      }))
    });
    await Promise.all(outsideSigners.map(fetchSignature(ix, outsidePayload)));
  } catch (error) {
    throw console.error("Signatures", error, {
      ix: ix
    }), error;
  }
  var tx;
  return ix;
}, debug("signatures", (ix, log, accts) => log(...accts(ix))), async function(ix) {
  for (let key of Object.keys(ix.accounts)) ix.accounts[key].addr = sansPrefix(ix.accounts[key].addr);
  return ix;
}, async function(ix) {
  return pipe(ix, get(ix, "ix.validators", []).map(cb => ix => cb(ix, {
    Ok: Ok,
    Bad: Bad
  })));
}, async function(ix) {
  const fn = get(ix, "ix.voucher-intercept");
  return isFn$3(fn) && await fn(createSignableVoucher(ix)), ix;
}, debug("resolved", (ix, log) => log(ix)) ]);
class SubscriptionsNotSupportedError extends Error {
  constructor() {
    super("The current transport does not support subscriptions.  If you have provided a custom transport (e.g. via `sdk.transport` configuration), ensure that it implements the subscribe method."), 
    this.name = "SubscriptionsNotSupportedError";
  }
}
async function getTransport(override = {}) {
  invariant(null == override.send || null == override.transport, 'SDK Transport Error: Cannot provide both "transport" and legacy "send" options.');
  const transportOrSend = override.transport || override.send || await config().first([ "sdk.transport", "sdk.send" ], httpTransport);
  return void 0 === (transport = transportOrSend).send || void 0 === transport.subscribe || "function" != typeof transport.send || "function" != typeof transport.subscribe ? {
    send: transportOrSend,
    subscribe: () => {
      throw new SubscriptionsNotSupportedError;
    }
  } : transportOrSend;
  var transport;
}
const send = async (args = [], opts = {}) => {
  const transport = await getTransport(opts), sendFn = transport.send.bind(transport), resolveFn = await config.first([ "sdk.resolve" ], opts.resolve || resolve);
  return opts.node = opts.node || await config().get("accessNode.api"), Array.isArray(args) && (args = pipe(initInteraction(), args)), 
  sendFn(await resolveFn(args), {
    config: config,
    response: response,
    ix: ixModule,
    Buffer: Buffer
  }, opts);
};
function limit(limit) {
  return ix => (ix.message.computeLimit = limit, ix);
}
export { build, limit, resolve, send };
//# sourceMappingURL=bundle.js.map
