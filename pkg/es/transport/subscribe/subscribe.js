import { subscribeRaw } from "./subscribe-raw.js";
import { decodeResponse } from "../../decode/decode.js";
/**
 * Subscribe to a topic and decode the data.
 * @param params - The parameters for the subscription.
 * @param opts - Additional options for the subscription.
 * @returns A promise that resolves when the subscription is active.
 */
export function subscribe({ topic, args, onData, onError }, opts = {}) {
    const sub = subscribeRaw({
        topic,
        args,
        onData: data => {
            decodeResponse(data)
                .then(onData)
                .catch(e => {
                onError(new Error(`Failed to decode response: ${e.message}`));
                sub?.unsubscribe?.();
            });
        },
        onError,
    }, opts);
    return sub;
}
