{"version": 3, "file": "bundle.js", "sources": ["es/resolve/resolve-accounts.js", "es/interaction/interaction.js", "es/build/build.js", "es/response/response.js", "es/build/build-get-block.js", "es/decode/decode-stream.js", "es/decode/decode.js", "es/resolve/resolve-cadence.js", "es/resolve/resolve-arguments.js", "es/encode/encode.js", "es/resolve/voucher.js", "es/resolve/resolve-signatures.js", "es/resolve/resolve.js", "es/resolve/resolve-compute-limit.js", "es/build/build-get-account.js", "es/resolve/resolve-final-normalization.js", "es/resolve/resolve-validators.js", "es/resolve/resolve-voucher-intercept.js", "es/transport/subscribe/errors.js", "es/transport/get-transport.js", "es/transport/send/send.js", "es/build/build-limit.js"], "sourcesContent": ["import { withPrefix } from \"@onflow/util-address\";\nimport { invariant } from \"@onflow/util-invariant\";\nimport { log } from \"@onflow/util-logger\";\nimport { isTransaction } from \"../interaction/interaction.js\";\nimport { createSignableVoucher } from \"./voucher.js\";\nimport { v4 as uuidv4 } from \"uuid\";\nconst MAX_DEPTH_LIMIT = 5;\nconst idof = (acct) => `${withPrefix(acct.addr)}-${acct.keyId}`;\nconst isFn = (v) => v &&\n    (Object.prototype.toString.call(v) === \"[object Function]\" ||\n        \"function\" === typeof v ||\n        v instanceof Function);\nconst genAccountId = (...ids) => ids.join(\"-\");\nvar ROLES;\n(function (ROLES) {\n    ROLES[\"PAYER\"] = \"payer\";\n    ROLES[\"PROPOSER\"] = \"proposer\";\n    ROLES[\"AUTHORIZATIONS\"] = \"authorizations\";\n})(ROLES || (ROLES = {}));\nfunction debug() {\n    const SPACE = \" \";\n    const SPACE_COUNT_PER_INDENT = 4;\n    const DEBUG_MESSAGE = [];\n    return [\n        function (msg = \"\", indent = 0) {\n            DEBUG_MESSAGE.push(Array(indent * SPACE_COUNT_PER_INDENT)\n                .fill(SPACE)\n                .join(\"-\") + msg);\n        },\n        function () {\n            return DEBUG_MESSAGE.reduce((prev, curr) => prev + \"\\n\" + curr);\n        },\n    ];\n}\nfunction recurseFlatMap(el, depthLimit = 3) {\n    if (depthLimit <= 0)\n        return el;\n    if (!Array.isArray(el))\n        return el;\n    return recurseFlatMap(el.flatMap(e => e), depthLimit - 1);\n}\nexport function buildPreSignable(acct, ix) {\n    try {\n        return {\n            f_type: \"PreSignable\",\n            f_vsn: \"1.0.1\",\n            roles: acct.role,\n            cadence: ix.message.cadence,\n            args: ix.message.arguments.map(d => ix.arguments[d].asArgument),\n            data: {},\n            interaction: ix,\n            voucher: createSignableVoucher(ix),\n        };\n    }\n    catch (error) {\n        console.error(\"buildPreSignable\", error);\n        throw error;\n    }\n}\nasync function removeUnusedIxAccounts(ix, opts) {\n    const payerTempIds = Array.isArray(ix.payer) ? ix.payer : [ix.payer];\n    const authorizersTempIds = Array.isArray(ix.authorizations)\n        ? ix.authorizations\n        : [ix.authorizations];\n    const proposerTempIds = ix.proposer === null\n        ? []\n        : Array.isArray(ix.proposer)\n            ? ix.proposer\n            : [ix.proposer];\n    const ixAccountKeys = Object.keys(ix.accounts);\n    const uniqueTempIds = [\n        ...new Set(payerTempIds.concat(authorizersTempIds, proposerTempIds)),\n    ];\n    for (const ixAccountKey of ixAccountKeys) {\n        if (!uniqueTempIds.find(id => id === ixAccountKey)) {\n            delete ix.accounts[ixAccountKey];\n        }\n    }\n}\nfunction addAccountToIx(ix, newAccount) {\n    if (typeof newAccount.addr === \"string\" &&\n        (typeof newAccount.keyId === \"number\" ||\n            typeof newAccount.keyId === \"string\")) {\n        newAccount.tempId = idof(newAccount);\n    }\n    else {\n        newAccount.tempId = newAccount.tempId || uuidv4();\n    }\n    const existingAccount = ix.accounts[newAccount.tempId] || newAccount;\n    if (!ix.accounts[newAccount.tempId]) {\n        ix.accounts[newAccount.tempId] = newAccount;\n    }\n    ix.accounts[newAccount.tempId].role.proposer =\n        existingAccount.role.proposer || newAccount.role.proposer;\n    ix.accounts[newAccount.tempId].role.payer =\n        existingAccount.role.payer || newAccount.role.payer;\n    ix.accounts[newAccount.tempId].role.authorizer =\n        existingAccount.role.authorizer || newAccount.role.authorizer;\n    return ix.accounts[newAccount.tempId];\n}\nfunction uniqueAccountsFlatMap(accounts) {\n    const flatMapped = recurseFlatMap(accounts);\n    const seen = new Set();\n    const uniqueAccountsFlatMapped = flatMapped\n        .map(account => {\n        const accountId = genAccountId(account.tempId, account.role.payer, account.role.proposer, account.role.authorizer, account.role.param);\n        if (seen.has(accountId))\n            return null;\n        seen.add(accountId);\n        return account;\n    })\n        .filter(e => e !== null);\n    return uniqueAccountsFlatMapped;\n}\n// Resolve single account, returns new account tempIds (if they exist)\nasync function resolveSingleAccount(ix, currentAccountTempId, depthLimit = MAX_DEPTH_LIMIT, { debugLogger }) {\n    if (depthLimit <= 0) {\n        throw new Error(`recurseResolveAccount Error: Depth limit (${MAX_DEPTH_LIMIT}) reached. Ensure your authorization functions resolve to an account after ${MAX_DEPTH_LIMIT} resolves.`);\n    }\n    let account = ix.accounts[currentAccountTempId];\n    if (!account)\n        return [[], false];\n    debugLogger(`account: ${account.tempId}`, Math.max(MAX_DEPTH_LIMIT - depthLimit, 0));\n    if (account?.resolve) {\n        if (isFn(account?.resolve)) {\n            debugLogger(`account: ${account.tempId} -- cache MISS`, Math.max(MAX_DEPTH_LIMIT - depthLimit, 0));\n            const { resolve, ...accountWithoutResolve } = account;\n            let resolvedAccounts = await resolve(accountWithoutResolve, buildPreSignable(accountWithoutResolve, ix));\n            resolvedAccounts = Array.isArray(resolvedAccounts)\n                ? resolvedAccounts\n                : [resolvedAccounts];\n            let flatResolvedAccounts = recurseFlatMap(resolvedAccounts);\n            flatResolvedAccounts = flatResolvedAccounts.map((flatResolvedAccount) => addAccountToIx(ix, flatResolvedAccount));\n            account.resolve = flatResolvedAccounts.map((flatResolvedAccount) => flatResolvedAccount.tempId);\n            account = addAccountToIx(ix, account);\n            return [\n                flatResolvedAccounts.map((flatResolvedAccount) => flatResolvedAccount.tempId),\n                true,\n            ];\n        }\n        else {\n            debugLogger(`account: ${account.tempId} -- cache HIT`, Math.max(MAX_DEPTH_LIMIT - depthLimit, 0));\n            return [account.resolve, false];\n        }\n    }\n    return [account.tempId ? [account.tempId] : [], false];\n}\nconst getAccountTempIDs = (rawTempIds) => {\n    if (rawTempIds === null) {\n        return [];\n    }\n    return Array.isArray(rawTempIds) ? rawTempIds : [rawTempIds];\n};\nasync function replaceRoles(ix, oldAccountTempId, newAccounts) {\n    // Replace roles in the interaction with any resolved accounts\n    // e.g. payer -> [oldAccountTempId, anotherId] => payer -> [newAccountTempId, anotherId]\n    for (let role of Object.values(ROLES)) {\n        if (role === ROLES.AUTHORIZATIONS || role === ROLES.PAYER) {\n            ix[role] = getAccountTempIDs(ix[role]).reduce((acc, acctTempId) => {\n                if (acctTempId === oldAccountTempId) {\n                    return acc.concat(...newAccounts\n                        .filter(x => {\n                        return ((role === ROLES.PAYER && x.role.payer) ||\n                            (role === ROLES.AUTHORIZATIONS && x.role.authorizer));\n                    })\n                        .map(acct => acct.tempId));\n                }\n                return acc.concat(acctTempId);\n            }, []);\n        }\n        else if (role === ROLES.PROPOSER) {\n            const proposerAccts = newAccounts.filter(x => x.role.proposer);\n            if (proposerAccts.length > 1) {\n                throw new Error(`replaceRoles Error: Multiple proposer keys were resolved, but only one is allowed`);\n            }\n            ix[role] = proposerAccts[0]?.tempId ?? ix[role];\n        }\n    }\n}\nasync function resolveAccountsByIds(ix, accountTempIds, depthLimit = MAX_DEPTH_LIMIT, { debugLogger }) {\n    invariant(ix && typeof ix === \"object\", \"resolveAccountType Error: ix not defined\");\n    let newTempIds = new Set();\n    for (let accountId of accountTempIds) {\n        let account = ix.accounts[accountId];\n        invariant(Boolean(account), `resolveAccountType Error: account not found`);\n        const [resolvedAccountTempIds, foundNewAccounts] = await resolveSingleAccount(ix, accountId, depthLimit, {\n            debugLogger,\n        });\n        // If new accounts were resolved, add them to the set so they can be explored next iteration\n        if (foundNewAccounts) {\n            const resolvedAccounts = resolvedAccountTempIds.map((resolvedAccountTempId) => ix.accounts[resolvedAccountTempId]);\n            const flatResolvedAccounts = uniqueAccountsFlatMap(resolvedAccounts);\n            // Add new tempIds to the set so they can be used next iteration\n            flatResolvedAccounts.forEach(x => newTempIds.add(x.tempId));\n            // Update any roles in the interaction based on the new accounts\n            replaceRoles(ix, accountId, flatResolvedAccounts);\n        }\n    }\n    // Ensure all payers are of the same account\n    let payerAddress;\n    for (const payerTempID of ix[ROLES.PAYER]) {\n        let pAcct = ix.accounts[payerTempID];\n        if (!payerAddress)\n            payerAddress = pAcct.addr;\n        else if (payerAddress !== pAcct.addr) {\n            throw new Error(\"resolveAccountType Error: payers from different accounts detected\");\n        }\n    }\n    return newTempIds;\n}\nexport async function resolveAccounts(ix, opts = {}) {\n    if (isTransaction(ix)) {\n        if (!Array.isArray(ix.payer)) {\n            log.deprecate({\n                pkg: \"FCL\",\n                subject: \"\\\"ix.payer\\\" must be an array. Support for ix.payer as a singular\",\n                message: \"See changelog for more info.\",\n            });\n        }\n        let [debugLogger, getDebugMessage] = debug();\n        try {\n            // BFS, resolving one level of accounts at a time\n            let depthLimit = MAX_DEPTH_LIMIT;\n            let frontier = new Set([\n                ...getAccountTempIDs(ix[ROLES.PAYER]),\n                ...getAccountTempIDs(ix[ROLES.PROPOSER]),\n                ...getAccountTempIDs(ix[ROLES.AUTHORIZATIONS]),\n            ]);\n            while (frontier.size > 0) {\n                if (depthLimit <= 0) {\n                    throw new Error(`resolveAccounts Error: Depth limit (${MAX_DEPTH_LIMIT}) reached. Ensure your authorization functions resolve to an account after ${MAX_DEPTH_LIMIT} resolves.`);\n                }\n                frontier = await resolveAccountsByIds(ix, frontier, depthLimit, {\n                    debugLogger,\n                });\n                depthLimit--;\n            }\n            await removeUnusedIxAccounts(ix, { debugLogger });\n            // Ensure at least one account for each role is resolved (except for authorizations)\n            for (const role of Object.values(ROLES)) {\n                invariant(getAccountTempIDs(ix[role]).length > 0 ||\n                    role === ROLES.AUTHORIZATIONS, `resolveAccountType Error: no accounts for role \"${role}\" found`);\n            }\n            if (opts.enableDebug) {\n                console.debug(getDebugMessage());\n            }\n        }\n        catch (error) {\n            console.error(\"=== SAD PANDA ===\\n\\n\", error, \"\\n\\n=== SAD PANDA ===\");\n            throw error;\n        }\n    }\n    return ix;\n}\n", "import { invariant } from \"@onflow/util-invariant\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport { log, LEVELS } from \"@onflow/util-logger\";\nimport { TransactionRole, InteractionResolverKind, InteractionStatus, InteractionTag, } from \"@onflow/typedefs\";\nconst ACCT = `{\n  \"kind\":\"${InteractionResolverKind.ACCOUNT}\",\n  \"tempId\":null,\n  \"addr\":null,\n  \"keyId\":null,\n  \"sequenceNum\":null,\n  \"signature\":null,\n  \"signingFunction\":null,\n  \"resolve\":null,\n  \"role\": {\n    \"proposer\":false,\n    \"authorizer\":false,\n    \"payer\":false,\n    \"param\":false\n  }\n}`;\nconst ARG = `{\n  \"kind\":\"${InteractionResolverKind.ARGUMENT}\",\n  \"tempId\":null,\n  \"value\":null,\n  \"asArgument\":null,\n  \"xform\":null,\n  \"resolve\": null,\n  \"resolveArgument\": null\n}`;\nconst IX = `{\n  \"tag\":\"${InteractionTag.UNKNOWN}\",\n  \"assigns\":{},\n  \"status\":\"${InteractionStatus.OK}\",\n  \"reason\":null,\n  \"accounts\":{},\n  \"params\":{},\n  \"arguments\":{},\n  \"message\": {\n    \"cadence\":null,\n    \"refBlock\":null,\n    \"computeLimit\":null,\n    \"proposer\":null,\n    \"payer\":null,\n    \"authorizations\":[],\n    \"params\":[],\n    \"arguments\":[]\n  },\n  \"proposer\":null,\n  \"authorizations\":[],\n  \"payer\":[],\n  \"events\": {\n    \"eventType\":null,\n    \"start\":null,\n    \"end\":null,\n    \"blockIds\":[]\n  },\n  \"subscribeEvents\": {\n    \"startBlockId\":null,\n    \"startHeight\":null,\n    \"eventTypes\":null,\n    \"addresses\":null,\n    \"contracts\":null,\n    \"heartbeatInterval\":null\n  },\n  \"transaction\": {\n    \"id\":null\n  },\n  \"block\": {\n    \"id\":null,\n    \"height\":null,\n    \"isSealed\":null\n  },\n  \"account\": {\n    \"addr\":null\n  },\n  \"collection\": {\n    \"id\":null\n  }\n}`;\nconst KEYS = new Set(Object.keys(JSON.parse(IX)));\nexport const initInteraction = () => JSON.parse(IX);\n/**\n * @deprecated\n */\nexport const interaction = () => {\n    log.deprecate({\n        pkg: \"FCL/SDK\",\n        message: `The interaction been deprecated from the Flow JS-SDK/FCL. use initInteraction instead`,\n        transition: \"https://github.com/onflow/flow-js-sdk/blob/master/packages/sdk/TRANSITIONS.md#0010-deprecate-interaction\",\n        level: LEVELS.warn,\n    });\n    return initInteraction();\n};\nexport const isNumber = (d) => typeof d === \"number\";\nexport const isArray = (d) => Array.isArray(d);\nexport const isObj = (d) => d !== null && typeof d === \"object\";\nexport const isNull = (d) => d == null;\nexport const isFn = (d) => typeof d === \"function\";\nexport const isInteraction = (ix) => {\n    if (!isObj(ix) || isNull(ix) || isNumber(ix))\n        return false;\n    for (let key of KEYS)\n        if (!ix.hasOwnProperty(key))\n            return false;\n    return true;\n};\nexport const Ok = (ix) => {\n    ix.status = InteractionStatus.OK;\n    return ix;\n};\nexport const Bad = (ix, reason) => {\n    ix.status = InteractionStatus.BAD;\n    ix.reason = reason;\n    return ix;\n};\nconst makeIx = (wat) => (ix) => {\n    ix.tag = wat;\n    return Ok(ix);\n};\nconst prepAccountKeyId = (acct) => {\n    if (acct.keyId == null)\n        return acct;\n    invariant(!isNaN(parseInt(acct.keyId.toString())), \"account.keyId must be an integer\");\n    return {\n        ...acct,\n        keyId: parseInt(acct.keyId.toString()),\n    };\n};\nexport const initAccount = () => JSON.parse(ACCT);\nexport const prepAccount = (acct, opts = {}) => (ix) => {\n    invariant(typeof acct === \"function\" || typeof acct === \"object\", \"prepAccount must be passed an authorization function or an account object\");\n    invariant(opts.role != null, \"Account must have a role\");\n    const ACCOUNT = initAccount();\n    const role = opts.role;\n    const tempId = uuidv4();\n    let account = { ...acct };\n    if (acct.authorization && isFn(acct.authorization))\n        account = { resolve: acct.authorization };\n    if (!acct.authorization && isFn(acct))\n        account = { resolve: acct };\n    const resolve = account.resolve;\n    if (resolve) {\n        account.resolve = (acct, ...rest) => [resolve, prepAccountKeyId].reduce(async (d, fn) => fn(await d, ...rest), acct);\n    }\n    account = prepAccountKeyId(account);\n    ix.accounts[tempId] = {\n        ...ACCOUNT,\n        tempId,\n        ...account,\n        role: {\n            ...ACCOUNT.role,\n            ...(typeof acct.role === \"object\" ? acct.role : {}),\n            ...(role ? { [role]: true } : {}),\n        },\n    };\n    if (role === TransactionRole.AUTHORIZER) {\n        ix.authorizations.push(tempId);\n    }\n    else if (role === TransactionRole.PAYER) {\n        ix.payer.push(tempId);\n    }\n    else if (role) {\n        ix[role] = tempId;\n    }\n    return ix;\n};\nexport const makeArgument = (arg) => (ix) => {\n    let tempId = uuidv4();\n    ix.message.arguments.push(tempId);\n    ix.arguments[tempId] = JSON.parse(ARG);\n    ix.arguments[tempId].tempId = tempId;\n    ix.arguments[tempId].value = arg.value;\n    ix.arguments[tempId].asArgument = arg.asArgument;\n    ix.arguments[tempId].xform = arg.xform;\n    ix.arguments[tempId].resolve = arg.resolve;\n    ix.arguments[tempId].resolveArgument = isFn(arg.resolveArgument)\n        ? arg.resolveArgument.bind(arg)\n        : arg.resolveArgument;\n    return Ok(ix);\n};\nexport const makeUnknown /*                 */ = makeIx(InteractionTag.UNKNOWN);\nexport const makeScript /*                  */ = makeIx(InteractionTag.SCRIPT);\nexport const makeTransaction /*             */ = makeIx(InteractionTag.TRANSACTION);\nexport const makeGetTransactionStatus /*    */ = makeIx(InteractionTag.GET_TRANSACTION_STATUS);\nexport const makeGetTransaction /*          */ = makeIx(InteractionTag.GET_TRANSACTION);\nexport const makeGetAccount /*              */ = makeIx(InteractionTag.GET_ACCOUNT);\nexport const makeGetEvents /*               */ = makeIx(InteractionTag.GET_EVENTS);\nexport const makePing /*                    */ = makeIx(InteractionTag.PING);\nexport const makeGetBlock /*                */ = makeIx(InteractionTag.GET_BLOCK);\nexport const makeGetBlockHeader /*          */ = makeIx(InteractionTag.GET_BLOCK_HEADER);\nexport const makeGetCollection /*           */ = makeIx(InteractionTag.GET_COLLECTION);\nexport const makeGetNetworkParameters /*    */ = makeIx(InteractionTag.GET_NETWORK_PARAMETERS);\nexport const makeSubscribeEvents /*         */ = makeIx(InteractionTag.SUBSCRIBE_EVENTS);\nexport const makeGetNodeVerionInfo /*       */ = makeIx(InteractionTag.GET_NODE_VERSION_INFO);\nconst is = (wat) => (ix) => ix.tag === wat;\nexport const isUnknown /*                 */ = is(InteractionTag.UNKNOWN);\nexport const isScript /*                  */ = is(InteractionTag.SCRIPT);\nexport const isTransaction /*             */ = is(InteractionTag.TRANSACTION);\nexport const isGetTransactionStatus /*    */ = is(InteractionTag.GET_TRANSACTION_STATUS);\nexport const isGetTransaction /*          */ = is(InteractionTag.GET_TRANSACTION);\nexport const isGetAccount /*              */ = is(InteractionTag.GET_ACCOUNT);\nexport const isGetEvents /*               */ = is(InteractionTag.GET_EVENTS);\nexport const isPing /*                    */ = is(InteractionTag.PING);\nexport const isGetBlock /*                */ = is(InteractionTag.GET_BLOCK);\nexport const isGetBlockHeader /*          */ = is(InteractionTag.GET_BLOCK_HEADER);\nexport const isGetCollection /*           */ = is(InteractionTag.GET_COLLECTION);\nexport const isGetNetworkParameters /*    */ = is(InteractionTag.GET_NETWORK_PARAMETERS);\nexport const isGetNodeVersionInfo /*      */ = is(InteractionTag.GET_NODE_VERSION_INFO);\nexport const isSubscribeEvents /*         */ = is(InteractionTag.SUBSCRIBE_EVENTS);\nexport const isOk /*  */ = (ix) => ix.status === InteractionStatus.OK;\nexport const isBad /* */ = (ix) => ix.status === InteractionStatus.BAD;\nexport const why /*   */ = (ix) => ix.reason;\nexport const isAccount /*  */ = (account) => account.kind === InteractionResolverKind.ACCOUNT;\nexport const isArgument /* */ = (argument) => argument.kind === InteractionResolverKind.ARGUMENT;\nconst hardMode = (ix) => {\n    for (let key of Object.keys(ix)) {\n        if (!KEYS.has(key))\n            throw new Error(`\"${key}\" is an invalid root level Interaction property.`);\n    }\n    return ix;\n};\nconst recPipe = async (ix, fns = []) => {\n    try {\n        ix = hardMode(await ix);\n        if (isBad(ix))\n            throw new Error(`Interaction Error: ${ix.reason}`);\n        if (!fns.length)\n            return ix;\n        const [hd, ...rest] = fns;\n        const cur = await hd;\n        if (isFn(cur))\n            return recPipe(cur(ix), rest);\n        if (isNull(cur) || !cur)\n            return recPipe(ix, rest);\n        if (isInteraction(cur))\n            return recPipe(cur, rest);\n        throw new Error(\"Invalid Interaction Composition\");\n    }\n    catch (e) {\n        throw e;\n    }\n};\nfunction pipe(...args) {\n    const [arg1, arg2] = args;\n    if (isArray(arg1))\n        return (d) => pipe(d, arg1);\n    const ix = arg1;\n    const fns = arg2;\n    return recPipe(ix, fns);\n}\nexport { pipe };\nconst identity = (v, ..._) => v;\nexport const get = (ix, key, fallback = undefined) => {\n    return ix.assigns[key] == null ? fallback : ix.assigns[key];\n};\nexport const put = (key, value) => (ix) => {\n    ix.assigns[key] = value;\n    return Ok(ix);\n};\nexport const update = (key, fn = identity) => (ix) => {\n    ix.assigns[key] = fn(ix.assigns[key], ix);\n    return Ok(ix);\n};\nexport const destroy = (key) => (ix) => {\n    delete ix.assigns[key];\n    return Ok(ix);\n};\n", "import { pipe, initInteraction, } from \"../interaction/interaction.js\";\n/**\n * @description A builder function that creates an interaction\n * @param fns The functions to apply to the interaction\n * @returns A promise of an interaction\n */\nexport function build(fns = []) {\n    return pipe(initInteraction(), fns);\n}\n", "const DEFAULT_RESPONSE = {\n    tag: null,\n    transaction: null,\n    transactionStatus: null,\n    transactionId: null,\n    encodedData: null,\n    events: null,\n    event: null,\n    accountStatusEvent: null,\n    account: null,\n    block: null,\n    blockHeader: null,\n    blockDigest: null,\n    latestBlock: null,\n    collection: null,\n    networkParameters: null,\n    streamConnection: null,\n    heartbeat: null,\n    nodeVersionInfo: null,\n};\nexport const response = () => ({ ...DEFAULT_RESPONSE });\n", "import { pipe, Ok, makeGetBlock, } from \"../interaction/interaction.js\";\n/**\n * @description A builder function that returns the interaction to get the latest block\n * @param isSealed Whether or not the block should be sealed\n * @returns A function that processes an interaction object\n */\nexport function getBlock(isSealed = null) {\n    return pipe([\n        makeGetBlock,\n        ix => {\n            ix.block.isSealed = isSealed;\n            return Ok(ix);\n        },\n    ]);\n}\n", "import EventEmitter from \"events\";\n/**\n * Pipes a generic stream of data into a granular stream of decoded data\n * The data is decoded per channel and emitted in order\n */\nexport const decodeStream = (stream, decodeResponse, customDecoders) => {\n    const newStream = new EventEmitter();\n    let queue = taskQueue();\n    // Data is separated by topic & the decoded data is emitted in order\n    // All topics for a given message will be emitted synchronously before moving on to the next message\n    // The streamReady promise ensures that the data is emitted in order and avoids race conditions when decoding\n    stream.on(\"data\", async (data) => {\n        const topics = Object.keys(data).filter(key => data[key] != null && key !== \"tag\");\n        let newDataPromise = Promise.all(topics.map(async (channel) => {\n            const partialResponse = {\n                [channel]: data[channel],\n            };\n            const message = await decodeResponse(partialResponse, customDecoders);\n            return {\n                channel,\n                message,\n            };\n        }));\n        queue.push(async () => {\n            // Emit the new data\n            const newData = await newDataPromise;\n            newData.forEach(({ channel, message }) => {\n                newStream.emit(channel, message);\n            });\n        });\n    });\n    // Relay events from the original stream\n    // These events are delivered in order as well so that the stream will\n    // not emit more data after it has announced a contradictory state\n    function relayEvent(event) {\n        stream.on(event, (message) => {\n            queue.push(async () => {\n                newStream.emit(event, message);\n            });\n        });\n    }\n    relayEvent(\"close\");\n    relayEvent(\"error\");\n    return {\n        on(channel, callback) {\n            newStream.on(channel, callback);\n            return this;\n        },\n        off(channel, callback) {\n            newStream.off(channel, callback);\n            return this;\n        },\n        close: () => {\n            stream.close();\n        },\n    };\n};\nfunction taskQueue() {\n    let queue = [];\n    let running = false;\n    async function run() {\n        if (running)\n            return;\n        running = true;\n        while (queue.length > 0) {\n            const task = queue.shift();\n            await task?.();\n        }\n        running = false;\n    }\n    return {\n        push: (task) => {\n            queue.push(task);\n            run();\n        },\n    };\n}\n", "import { log } from \"@onflow/util-logger\";\nimport { decodeStream } from \"./decode-stream.js\";\nconst latestBlockDeprecationNotice = () => {\n    log.deprecate({\n        pkg: \"@onflow/decode\",\n        subject: \"Operating upon data of the latestBlock field of the response object\",\n        transition: \"https://github.com/onflow/flow-js-sdk/blob/master/packages/decode/WARNINGS.md#0001-Deprecating-latestBlock-field\",\n    });\n};\nconst decodeNumber = async (num, _, stack) => {\n    try {\n        return Number(num);\n    }\n    catch (e) {\n        throw new Error(`Decode Number Error : ${stack.join(\".\")}`);\n    }\n};\nconst decodeImplicit = async (i) => i;\nconst decodeVoid = async () => null;\nconst decodeType = async (type) => {\n    return type.staticType;\n};\nconst decodeOptional = async (optional, decoders, stack) => optional ? await recurseDecode(optional, decoders, stack) : null;\nconst decodeArray = async (array, decoders, stack) => await Promise.all(array.map(v => new Promise(async (res) => res(await recurseDecode(v, decoders, [...stack, v.type])))));\nconst decodeDictionary = async (dictionary, decoders, stack) => await dictionary.reduce(async (acc, v) => {\n    acc = await acc;\n    acc[await recurseDecode(v.key, decoders, [...stack, v.key])] =\n        await recurseDecode(v.value, decoders, [...stack, v.key]);\n    return acc;\n}, Promise.resolve({}));\nconst decodeComposite = async (composite, decoders, stack) => {\n    const decoded = await composite.fields.reduce(async (acc, v) => {\n        acc = await acc;\n        acc[v.name] = await recurseDecode(v.value, decoders, [...stack, v.name]);\n        return acc;\n    }, Promise.resolve({}));\n    const decoder = composite.id && decoderLookup(decoders, composite.id);\n    return decoder ? await decoder(decoded) : decoded;\n};\nconst decodeInclusiveRange = async (range, decoders, stack) => {\n    // Recursive decode for start, end, and step\n    // We don't do all fields just in case there are future API changes\n    // where fields added and are not Cadence values\n    const keys = [\"start\", \"end\", \"step\"];\n    const decoded = await Object.keys(range).reduce(async (acc, key) => {\n        acc = await acc;\n        if (keys.includes(key)) {\n            acc[key] = await recurseDecode(range[key], decoders, [...stack, key]);\n        }\n        return acc;\n    }, Promise.resolve({}));\n    return decoded;\n};\nconst defaultDecoders = {\n    UInt: decodeImplicit,\n    Int: decodeImplicit,\n    UInt8: decodeImplicit,\n    Int8: decodeImplicit,\n    UInt16: decodeImplicit,\n    Int16: decodeImplicit,\n    UInt32: decodeImplicit,\n    Int32: decodeImplicit,\n    UInt64: decodeImplicit,\n    Int64: decodeImplicit,\n    UInt128: decodeImplicit,\n    Int128: decodeImplicit,\n    UInt256: decodeImplicit,\n    Int256: decodeImplicit,\n    Word8: decodeImplicit,\n    Word16: decodeImplicit,\n    Word32: decodeImplicit,\n    Word64: decodeImplicit,\n    Word128: decodeImplicit,\n    Word256: decodeImplicit,\n    UFix64: decodeImplicit,\n    Fix64: decodeImplicit,\n    String: decodeImplicit,\n    Character: decodeImplicit,\n    Bool: decodeImplicit,\n    Address: decodeImplicit,\n    Void: decodeVoid,\n    Optional: decodeOptional,\n    Reference: decodeImplicit,\n    Array: decodeArray,\n    Dictionary: decodeDictionary,\n    Event: decodeComposite,\n    Resource: decodeComposite,\n    Struct: decodeComposite,\n    Enum: decodeComposite,\n    Type: decodeType,\n    Path: decodeImplicit,\n    Capability: decodeImplicit,\n    InclusiveRange: decodeInclusiveRange,\n};\nconst decoderLookup = (decoders, lookup) => {\n    const found = Object.keys(decoders).find(decoder => {\n        if (/^\\/.*\\/$/.test(decoder)) {\n            const reg = new RegExp(decoder.substring(1, decoder.length - 1));\n            return reg.test(lookup);\n        }\n        return decoder === lookup;\n    });\n    return lookup && found && decoders[found];\n};\nconst recurseDecode = async (decodeInstructions, decoders, stack) => {\n    let decoder = decoderLookup(decoders, decodeInstructions.type);\n    if (!decoder)\n        throw new Error(`Undefined Decoder Error: ${decodeInstructions.type}@${stack.join(\".\")}`);\n    return await decoder(decodeInstructions.value, decoders, stack);\n};\n/**\n * @description - Decodes a response from Flow into JSON\n * @param decodeInstructions - The response object from Flow\n * @param customDecoders - An object of custom decoders\n * @param stack - The stack of the current decoding\n * @returns - The decoded response\n */\nexport const decode = async (decodeInstructions, customDecoders = {}, stack = []) => {\n    // Filter out all default decoders which are overridden by a custom decoder regex\n    const filteredDecoders = Object.keys(defaultDecoders)\n        .filter(decoder => !Object.keys(customDecoders).find(customDecoder => new RegExp(customDecoder).test(decoder)))\n        .reduce((decoders, decoderKey) => {\n        decoders[decoderKey] = defaultDecoders[decoderKey];\n        return decoders;\n    }, customDecoders);\n    const decoders = {\n        ...filteredDecoders,\n        ...customDecoders,\n    };\n    return recurseDecode(decodeInstructions, decoders, stack);\n};\nexport const decodeResponse = async (response, customDecoders = {}) => {\n    if (response.encodedData) {\n        return decode(response.encodedData, customDecoders);\n    }\n    else if (response.transactionStatus) {\n        return {\n            ...response.transactionStatus,\n            events: await Promise.all(response.transactionStatus.events.map(async function decodeEvents(e) {\n                return {\n                    type: e.type,\n                    transactionId: e.transactionId,\n                    transactionIndex: e.transactionIndex,\n                    eventIndex: e.eventIndex,\n                    data: await decode(e.payload, customDecoders),\n                };\n            })),\n        };\n    }\n    else if (response.transaction) {\n        return response.transaction;\n    }\n    else if (response.events) {\n        return await Promise.all(response.events.map(async function decodeEvents(e) {\n            return {\n                blockId: e.blockId,\n                blockHeight: e.blockHeight,\n                blockTimestamp: e.blockTimestamp,\n                type: e.type,\n                transactionId: e.transactionId,\n                transactionIndex: e.transactionIndex,\n                eventIndex: e.eventIndex,\n                data: await decode(e.payload, customDecoders),\n            };\n        }));\n    }\n    else if (response.event) {\n        const { payload, ...rest } = response.event;\n        return {\n            ...rest,\n            data: await decode(payload, customDecoders),\n        };\n    }\n    else if (response.accountStatusEvent) {\n        const { payload, ...rest } = response.accountStatusEvent;\n        return {\n            ...rest,\n            data: await decode(payload, customDecoders),\n        };\n    }\n    else if (response.account) {\n        return response.account;\n    }\n    else if (response.block) {\n        return response.block;\n    }\n    else if (response.blockHeader) {\n        return response.blockHeader;\n    }\n    else if (response.blockDigest) {\n        return response.blockDigest;\n    }\n    else if (response.latestBlock) {\n        latestBlockDeprecationNotice();\n        return response.latestBlock;\n    }\n    else if (response.transactionId) {\n        return response.transactionId;\n    }\n    else if (response.collection) {\n        return response.collection;\n    }\n    else if (response.networkParameters) {\n        const prefixRegex = /^flow-/;\n        const rawChainId = response.networkParameters.chainId;\n        let formattedChainId;\n        if (rawChainId === \"flow-emulator\") {\n            formattedChainId = \"local\";\n        }\n        else if (prefixRegex.test(rawChainId)) {\n            formattedChainId = rawChainId.replace(prefixRegex, \"\");\n        }\n        else {\n            formattedChainId = rawChainId;\n        }\n        return {\n            chainId: formattedChainId,\n        };\n    }\n    else if (response.streamConnection) {\n        return decodeStream(response.streamConnection, decodeResponse, customDecoders);\n    }\n    else if (response.heartbeat) {\n        return response.heartbeat;\n    }\n    else if (response.nodeVersionInfo) {\n        return response.nodeVersionInfo;\n    }\n    return null;\n};\n", "import { isTransaction, isScript, get } from \"../interaction/interaction.js\";\nimport { invariant } from \"@onflow/util-invariant\";\nimport { config } from \"@onflow/config\";\nimport * as logger from \"@onflow/util-logger\";\nimport { withPrefix } from \"@onflow/util-address\";\nconst isFn = (v) => typeof v === \"function\";\nconst isString = (v) => typeof v === \"string\";\nconst oldIdentifierPatternFn = () => /\\b(0x\\w+)\\b/g;\nfunction isOldIdentifierSyntax(cadence) {\n    return oldIdentifierPatternFn().test(cadence);\n}\nconst newIdentifierPatternFn = () => /import\\s+\"(\\w+)\"/g;\nfunction isNewIdentifierSyntax(cadence) {\n    return newIdentifierPatternFn().test(cadence);\n}\nfunction getContractIdentifierSyntaxMatches(cadence) {\n    return cadence.matchAll(newIdentifierPatternFn());\n}\nexport async function resolveCadence(ix) {\n    if (!isTransaction(ix) && !isScript(ix))\n        return ix;\n    var cadence = get(ix, \"ix.cadence\");\n    invariant(isFn(cadence) || isString(cadence), \"Cadence needs to be a function or a string.\");\n    if (isFn(cadence))\n        cadence = await cadence({});\n    invariant(isString(cadence), \"Cadence needs to be a string at this point.\");\n    invariant(!isOldIdentifierSyntax(cadence) || !isNewIdentifierSyntax(cadence), \"Both account identifier and contract identifier syntax not simultaneously supported.\");\n    if (isOldIdentifierSyntax(cadence)) {\n        cadence = await config()\n            .where(/^0x/)\n            .then(d => Object.entries(d).reduce((cadence, [key, value]) => {\n            const regex = new RegExp(\"(\\\\b\" + key + \"\\\\b)\", \"g\");\n            return cadence.replace(regex, value);\n        }, cadence));\n    }\n    if (isNewIdentifierSyntax(cadence)) {\n        for (const [fullMatch, contractName] of getContractIdentifierSyntaxMatches(cadence)) {\n            const address = await config().get(`system.contracts.${contractName}`);\n            if (address) {\n                cadence = cadence.replace(fullMatch, `import ${contractName} from ${withPrefix(address)}`);\n            }\n            else {\n                logger.log({\n                    title: \"Contract Placeholder not found\",\n                    message: `Cannot find a value for contract placeholder ${contractName}. Please add to your flow.json or explicitly add it to the config 'contracts.*' namespace.`,\n                    level: logger.LEVELS.warn,\n                });\n            }\n        }\n    }\n    // We need to move this over in any case.\n    ix.message.cadence = cadence;\n    return ix;\n}\n", "import { invariant } from \"@onflow/util-invariant\";\nimport { isTransaction, isScript } from \"../interaction/interaction.js\";\nconst isFn = (v) => typeof v === \"function\";\nfunction cast(arg) {\n    // prettier-ignore\n    invariant(typeof arg.xform != null, `No type specified for argument: ${arg.value}`);\n    if (isFn(arg.xform))\n        return arg.xform(arg.value);\n    if (isFn(arg.xform.asArgument))\n        return arg.xform.asArgument(arg.value);\n    // prettier-ignore\n    invariant(false, `Invalid Argument`, arg);\n}\nasync function handleArgResolution(arg, depth = 3) {\n    invariant(depth > 0, `Argument Resolve Recursion Limit Exceeded for Arg: ${arg.tempId}`);\n    if (isFn(arg.resolveArgument)) {\n        const resolvedArg = await arg.resolveArgument();\n        return handleArgResolution(resolvedArg, depth - 1);\n    }\n    else {\n        return arg;\n    }\n}\nexport async function resolveArguments(ix) {\n    if (isTransaction(ix) || isScript(ix)) {\n        for (let [id, arg] of Object.entries(ix.arguments)) {\n            const res = await handleArgResolution(arg);\n            ix.arguments[id].asArgument = cast(res);\n        }\n    }\n    return ix;\n}\n", "import { SHA3 } from \"sha3\";\nimport { encode, Buffer } from \"@onflow/rlp\";\nimport { sansPrefix } from \"@onflow/util-address\";\nexport const encodeTransactionPayload = (tx) => prependTransactionDomainTag(rlpEncode(preparePayload(tx)));\nexport const encodeTransactionEnvelope = (tx) => prependTransactionDomainTag(rlpEncode(prepareEnvelope(tx)));\nexport const encodeTxIdFromVoucher = (voucher) => sha3_256(rlpEncode(prepareVoucher(voucher)));\nconst rightPaddedHexBuffer = (value, pad) => Buffer.from(value.padEnd(pad * 2, \"0\"), \"hex\");\nconst leftPaddedHexBuffer = (value, pad) => Buffer.from(value.padStart(pad * 2, \"0\"), \"hex\");\nconst TRANSACTION_DOMAIN_TAG = rightPaddedHexBuffer(Buffer.from(\"FLOW-V0.0-transaction\").toString(\"hex\"), 32).toString(\"hex\");\nconst prependTransactionDomainTag = (tx) => TRANSACTION_DOMAIN_TAG + tx;\nconst addressBuffer = (addr) => leftPaddedHexBuffer(addr, 8);\nconst blockBuffer = (block) => leftPaddedHexBuffer(block, 32);\nconst argumentToString = (arg) => Buffer.from(JSON.stringify(arg), \"utf8\");\nconst scriptBuffer = (script) => Buffer.from(script, \"utf8\");\nconst signatureBuffer = (signature) => Buffer.from(signature, \"hex\");\nconst rlpEncode = (v) => {\n    return encode(v).toString(\"hex\");\n};\nconst sha3_256 = (msg) => {\n    const sha = new SHA3(256);\n    sha.update(Buffer.from(msg, \"hex\"));\n    return sha.digest().toString(\"hex\");\n};\nconst preparePayload = (tx) => {\n    validatePayload(tx);\n    return [\n        scriptBuffer(tx.cadence || \"\"),\n        tx.arguments.map(argumentToString),\n        blockBuffer(tx.refBlock || \"\"),\n        tx.computeLimit,\n        addressBuffer(sansPrefix(tx.proposalKey.address || \"\")),\n        tx.proposalKey.keyId,\n        tx.proposalKey.sequenceNum,\n        addressBuffer(sansPrefix(tx.payer)),\n        tx.authorizers.map(authorizer => addressBuffer(sansPrefix(authorizer))),\n    ];\n};\nconst prepareEnvelope = (tx) => {\n    validateEnvelope(tx);\n    return [preparePayload(tx), preparePayloadSignatures(tx)];\n};\nconst preparePayloadSignatures = (tx) => {\n    const signers = collectSigners(tx);\n    return tx.payloadSigs\n        ?.map((sig) => {\n        return {\n            signerIndex: signers.get(sansPrefix(sig.address)) || \"\",\n            keyId: sig.keyId,\n            sig: sig.sig,\n        };\n    })\n        .sort((a, b) => {\n        if (a.signerIndex > b.signerIndex)\n            return 1;\n        if (a.signerIndex < b.signerIndex)\n            return -1;\n        if (a.keyId > b.keyId)\n            return 1;\n        if (a.keyId < b.keyId)\n            return -1;\n        return 0;\n    })\n        .map(sig => {\n        return [sig.signerIndex, sig.keyId, signatureBuffer(sig.sig)];\n    });\n};\nconst collectSigners = (tx) => {\n    const signers = new Map();\n    let i = 0;\n    const addSigner = (addr) => {\n        if (!signers.has(addr)) {\n            signers.set(addr, i);\n            i++;\n        }\n    };\n    if (tx.proposalKey.address) {\n        addSigner(tx.proposalKey.address);\n    }\n    addSigner(tx.payer);\n    tx.authorizers.forEach(addSigner);\n    return signers;\n};\nconst prepareVoucher = (voucher) => {\n    validateVoucher(voucher);\n    const signers = collectSigners(voucher);\n    const prepareSigs = (sigs) => {\n        return sigs\n            .map(({ address, keyId, sig }) => {\n            return { signerIndex: signers.get(sansPrefix(address)) || \"\", keyId, sig };\n        })\n            .sort((a, b) => {\n            if (a.signerIndex > b.signerIndex)\n                return 1;\n            if (a.signerIndex < b.signerIndex)\n                return -1;\n            if (a.keyId > b.keyId)\n                return 1;\n            if (a.keyId < b.keyId)\n                return -1;\n            return 0;\n        })\n            .map(sig => {\n            return [sig.signerIndex, sig.keyId, signatureBuffer(sig.sig)];\n        });\n    };\n    return [\n        [\n            scriptBuffer(voucher.cadence),\n            voucher.arguments.map(argumentToString),\n            blockBuffer(voucher.refBlock),\n            voucher.computeLimit,\n            addressBuffer(sansPrefix(voucher.proposalKey.address)),\n            voucher.proposalKey.keyId,\n            voucher.proposalKey.sequenceNum,\n            addressBuffer(sansPrefix(voucher.payer)),\n            voucher.authorizers.map(authorizer => addressBuffer(sansPrefix(authorizer))),\n        ],\n        prepareSigs(voucher.payloadSigs),\n        prepareSigs(voucher.envelopeSigs),\n    ];\n};\nconst validatePayload = (tx) => {\n    payloadFields.forEach(field => checkField(tx, field));\n    proposalKeyFields.forEach(field => checkField(tx.proposalKey, field, \"proposalKey\"));\n};\nconst validateEnvelope = (tx) => {\n    payloadSigsFields.forEach(field => checkField(tx, field));\n    tx.payloadSigs?.forEach((sig, index) => {\n        payloadSigFields.forEach(field => checkField(sig, field, \"payloadSigs\", index));\n    });\n};\nconst validateVoucher = (voucher) => {\n    payloadFields.forEach(field => checkField(voucher, field));\n    proposalKeyFields.forEach(field => checkField(voucher.proposalKey, field, \"proposalKey\"));\n    payloadSigsFields.forEach(field => checkField(voucher, field));\n    voucher.payloadSigs.forEach((sig, index) => {\n        payloadSigFields.forEach(field => checkField(sig, field, \"payloadSigs\", index));\n    });\n    envelopeSigsFields.forEach(field => checkField(voucher, field));\n    voucher.envelopeSigs.forEach((sig, index) => {\n        envelopeSigFields.forEach(field => checkField(sig, field, \"envelopeSigs\", index));\n    });\n};\nconst isNumber = (v) => typeof v === \"number\";\nconst isString = (v) => typeof v === \"string\";\nconst isObject = (v) => v !== null && typeof v === \"object\";\nconst isArray = (v) => isObject(v) && v instanceof Array;\nconst payloadFields = [\n    { name: \"cadence\", check: isString },\n    { name: \"arguments\", check: isArray },\n    { name: \"refBlock\", check: isString, defaultVal: \"0\" },\n    { name: \"computeLimit\", check: isNumber },\n    { name: \"proposalKey\", check: isObject },\n    { name: \"payer\", check: isString },\n    { name: \"authorizers\", check: isArray },\n];\nconst proposalKeyFields = [\n    { name: \"address\", check: isString },\n    { name: \"keyId\", check: isNumber },\n    { name: \"sequenceNum\", check: isNumber },\n];\nconst payloadSigsFields = [\n    { name: \"payloadSigs\", check: isArray },\n];\nconst payloadSigFields = [\n    { name: \"address\", check: isString },\n    { name: \"keyId\", check: isNumber },\n    { name: \"sig\", check: isString },\n];\nconst envelopeSigsFields = [\n    { name: \"envelopeSigs\", check: isArray },\n];\nconst envelopeSigFields = [\n    { name: \"address\", check: isString },\n    { name: \"keyId\", check: isNumber },\n    { name: \"sig\", check: isString },\n];\nconst checkField = (obj, field, base, index) => {\n    const { name, check, defaultVal } = field;\n    if (obj[name] == null && defaultVal != null)\n        obj[name] = defaultVal;\n    if (obj[name] == null)\n        throw missingFieldError(name, base, index);\n    if (!check(obj[name]))\n        throw invalidFieldError(name, base, index);\n};\nconst printFieldName = (field, base, index) => {\n    if (!!base)\n        return index == null ? `${base}.${field}` : `${base}.${index}.${field}`;\n    return field;\n};\nconst missingFieldError = (field, base, index) => new Error(`Missing field ${printFieldName(field, base, index)}`);\nconst invalidFieldError = (field, base, index) => new Error(`Invalid field ${printFieldName(field, base, index)}`);\n", "import { withPrefix } from \"@onflow/util-address\";\nimport { encodeTxIdFromVoucher } from \"../encode/encode.js\";\nexport function findInsideSigners(ix) {\n    // Inside Signers Are: (authorizers + proposer) - payer\n    let inside = new Set(ix.authorizations);\n    if (ix.proposer) {\n        inside.add(ix.proposer);\n    }\n    if (Array.isArray(ix.payer)) {\n        ix.payer.forEach(p => inside.delete(p));\n    }\n    else {\n        inside.delete(ix.payer);\n    }\n    return Array.from(inside);\n}\nexport function findOutsideSigners(ix) {\n    // Outside Signers Are: (payer)\n    let outside = new Set(Array.isArray(ix.payer) ? ix.payer : [ix.payer]);\n    return Array.from(outside);\n}\nexport const createSignableVoucher = (ix) => {\n    const buildAuthorizers = () => {\n        const authorizations = ix.authorizations\n            .map(cid => withPrefix(ix.accounts[cid].addr))\n            .reduce((prev, current) => {\n            return prev.find(item => item === current) ? prev : [...prev, current];\n        }, []);\n        return authorizations;\n    };\n    const buildInsideSigners = () => findInsideSigners(ix).map(id => ({\n        address: withPrefix(ix.accounts[id].addr),\n        keyId: ix.accounts[id].keyId,\n        sig: ix.accounts[id].signature,\n    }));\n    const buildOutsideSigners = () => findOutsideSigners(ix).map(id => ({\n        address: withPrefix(ix.accounts[id].addr),\n        keyId: ix.accounts[id].keyId,\n        sig: ix.accounts[id].signature,\n    }));\n    const proposalKey = ix.proposer\n        ? {\n            address: withPrefix(ix.accounts[ix.proposer].addr),\n            keyId: ix.accounts[ix.proposer].keyId,\n            sequenceNum: ix.accounts[ix.proposer].sequenceNum,\n        }\n        : {};\n    return {\n        cadence: ix.message.cadence,\n        refBlock: ix.message.refBlock || null,\n        computeLimit: ix.message.computeLimit,\n        arguments: ix.message.arguments.map(id => ix.arguments[id].asArgument),\n        proposalKey,\n        payer: withPrefix(ix.accounts[Array.isArray(ix.payer) ? ix.payer[0] : ix.payer].addr),\n        authorizers: buildAuthorizers(),\n        payloadSigs: buildInsideSigners(),\n        envelopeSigs: buildOutsideSigners(),\n    };\n};\nexport const voucherToTxId = (voucher) => {\n    return encodeTxIdFromVoucher(voucher);\n};\n", "import { isTransaction } from \"../interaction/interaction.js\";\nimport { sansPrefix } from \"@onflow/util-address\";\nimport { encodeTransactionPayload as encodeInsideMessage, encodeTransactionEnvelope as encodeOutsideMessage, } from \"../encode/encode.js\";\nimport { createSignableVoucher, findInsideSigners, findOutsideSigners, } from \"./voucher.js\";\nexport async function resolveSignatures(ix) {\n    if (isTransaction(ix)) {\n        try {\n            let insideSigners = findInsideSigners(ix);\n            const insidePayload = encodeInsideMessage(prepForEncoding(ix));\n            // Promise.all could potentially break the flow if there are multiple inside signers trying to resolve at the same time\n            // causing multiple triggers of authz function that tries to render multiple auth iiframes/tabs/extensions\n            // as an alternative, use this:\n            // for(const insideSigner of insideSigners) {\n            //   await fetchSignature(ix, insidePayload)(insideSigner);\n            // }\n            await Promise.all(insideSigners.map(fetchSignature(ix, insidePayload)));\n            let outsideSigners = findOutsideSigners(ix);\n            const outsidePayload = encodeOutsideMessage({\n                ...prepForEncoding(ix),\n                payloadSigs: insideSigners.map(id => ({\n                    address: ix.accounts[id].addr || \"\",\n                    keyId: ix.accounts[id].keyId || 0,\n                    sig: ix.accounts[id].signature || \"\",\n                })),\n            });\n            // Promise.all could potentially break the flow if there are multiple outside signers trying to resolve at the same time\n            // causing multiple triggers of authz function that tries to render multiple auth iframes/tabs/extensions\n            // as an alternative, use this:\n            // for(const outsideSigner of outsideSigners) {\n            //   await fetchSignature(ix, outsidePayload)(outsideSigner);\n            // }\n            await Promise.all(outsideSigners.map(fetchSignature(ix, outsidePayload)));\n        }\n        catch (error) {\n            console.error(\"Signatures\", error, { ix });\n            throw error;\n        }\n    }\n    return ix;\n}\nfunction fetchSignature(ix, payload) {\n    return async function innerFetchSignature(id) {\n        const acct = ix.accounts[id];\n        if (acct.signature != null && acct.signature !== undefined)\n            return;\n        const { signature } = await acct.signingFunction(buildSignable(acct, payload, ix));\n        ix.accounts[id].signature = signature;\n    };\n}\nexport function buildSignable(acct, message, ix) {\n    try {\n        return {\n            f_type: \"Signable\",\n            f_vsn: \"1.0.1\",\n            message,\n            addr: sansPrefix(acct.addr),\n            keyId: acct.keyId,\n            roles: acct.role,\n            cadence: ix.message.cadence,\n            args: ix.message.arguments.map(d => ix.arguments[d].asArgument),\n            data: {},\n            interaction: ix,\n            voucher: createSignableVoucher(ix),\n        };\n    }\n    catch (error) {\n        console.error(\"buildSignable\", error);\n        throw error;\n    }\n}\nfunction prepForEncoding(ix) {\n    const payerAddress = sansPrefix((Array.isArray(ix.payer) ? ix.accounts[ix.payer[0]] : ix.accounts[ix.payer])\n        .addr || \"\");\n    const proposalKey = ix.proposer\n        ? {\n            address: sansPrefix(ix.accounts[ix.proposer].addr) || \"\",\n            keyId: ix.accounts[ix.proposer].keyId || 0,\n            sequenceNum: ix.accounts[ix.proposer].sequenceNum || 0,\n        }\n        : {};\n    return {\n        cadence: ix.message.cadence,\n        refBlock: ix.message.refBlock,\n        computeLimit: ix.message.computeLimit,\n        arguments: ix.message.arguments.map(id => ix.arguments[id].asArgument),\n        proposalKey,\n        payer: payerAddress,\n        authorizers: ix.authorizations\n            .map(cid => sansPrefix(ix.accounts[cid].addr) || \"\")\n            .reduce((prev, current) => {\n            return prev.find(item => item === current) ? prev : [...prev, current];\n        }, []),\n    };\n}\n", "import { pipe, isTransaction } from \"../interaction/interaction.js\";\nimport { config } from \"@onflow/config\";\nimport { invariant } from \"@onflow/util-invariant\";\nimport { <PERSON>uffer } from \"@onflow/rlp\";\nimport { send as defaultSend } from \"@onflow/transport-http\";\nimport * as ixModule from \"../interaction/interaction.js\";\nimport { response } from \"../response/response.js\";\nimport { build } from \"../build/build.js\";\nimport { getBlock } from \"../build/build-get-block.js\";\nimport { getAccount } from \"../build/build-get-account.js\";\nimport { decodeResponse as decode } from \"../decode/decode.js\";\nimport { resolveCadence } from \"./resolve-cadence.js\";\nimport { resolveArguments } from \"./resolve-arguments.js\";\nimport { resolveAccounts } from \"./resolve-accounts.js\";\nimport { resolveSignatures } from \"./resolve-signatures.js\";\nimport { resolveValidators } from \"./resolve-validators.js\";\nimport { resolveFinalNormalization } from \"./resolve-final-normalization.js\";\nimport { resolveVoucherIntercept } from \"./resolve-voucher-intercept.js\";\nimport { resolveComputeLimit } from \"./resolve-compute-limit.js\";\nconst noop = (v) => v;\nconst debug = (key, fn = noop) => async (ix) => {\n    const take = (obj, keys = []) => {\n        if (typeof keys === \"string\")\n            keys = keys.split(\" \");\n        keys.reduce((acc, key) => ({ ...acc, [key]: obj[key] }), {});\n    };\n    const accts = (ix) => [\n        \"\\nAccounts:\",\n        {\n            proposer: ix.proposer,\n            authorizations: ix.authorizations,\n            payer: ix.payer,\n        },\n        \"\\n\\nDetails:\",\n        ix.accounts,\n    ].filter(Boolean);\n    const log = (...msg) => {\n        console.log(`debug[${key}] ---\\n`, ...msg, \"\\n\\n\\n---\");\n    };\n    if (await config.get(`debug.${key}`))\n        await fn(ix, log, accts);\n    return ix;\n};\nexport const resolve = pipe([\n    resolveCadence,\n    debug(\"cadence\", (ix, log) => log(ix.message.cadence)),\n    resolveComputeLimit,\n    debug(\"compute limit\", (ix, log) => log(ix.message.computeLimit)),\n    resolveArguments,\n    debug(\"arguments\", (ix, log) => log(ix.message.arguments, ix.message)),\n    resolveAccounts,\n    debug(\"accounts\", (ix, log, accts) => log(...accts(ix))),\n    /* special */ execFetchRef,\n    /* special */ execFetchSequenceNumber,\n    resolveSignatures,\n    debug(\"signatures\", (ix, log, accts) => log(...accts(ix))),\n    resolveFinalNormalization,\n    resolveValidators,\n    resolveVoucherIntercept,\n    debug(\"resolved\", (ix, log) => log(ix)),\n]);\nasync function execFetchRef(ix) {\n    if (isTransaction(ix) && ix.message.refBlock == null) {\n        const node = await config().get(\"accessNode.api\");\n        const sendFn = await config.first([\"sdk.transport\", \"sdk.send\"], defaultSend);\n        invariant(sendFn, `Required value for sdk.transport is not defined in config. See: ${\"https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21\"}`);\n        ix.message.refBlock = (await sendFn(build([getBlock()]), { config, response, Buffer, ix: ixModule }, { node }).then(decode)).id;\n    }\n    return ix;\n}\nasync function execFetchSequenceNumber(ix) {\n    if (isTransaction(ix)) {\n        var acct = Object.values(ix.accounts).find((a) => a.role.proposer);\n        invariant(acct !== undefined, `Transactions require a proposer`);\n        if (acct && acct.sequenceNum == null) {\n            const node = await config().get(\"accessNode.api\");\n            const sendFn = await config.first([\"sdk.transport\", \"sdk.send\"], defaultSend);\n            invariant(sendFn, `Required value for sdk.transport is not defined in config. See: ${\"https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21\"}`);\n            ix.accounts[acct.tempId].sequenceNum = await sendFn(await build([getAccount(acct.addr)]), { config, response, Buffer, ix: ixModule }, { node })\n                .then(decode)\n                .then((acctResponse) => acctResponse.keys)\n                .then((keys) => keys.find((key) => key.index === acct.keyId))\n                .then((key) => key.sequenceNumber);\n        }\n    }\n    return ix;\n}\n", "import { isTransaction } from \"../interaction/interaction.js\";\nimport { config } from \"@onflow/config\";\nimport * as logger from \"@onflow/util-logger\";\nconst DEFAULT_COMPUTE_LIMIT = 100;\nexport async function resolveComputeLimit(ix) {\n    if (isTransaction(ix)) {\n        ix.message.computeLimit =\n            ix.message.computeLimit || (await config.get(\"fcl.limit\"));\n        if (!ix.message.computeLimit) {\n            logger.log.deprecate({\n                pkg: \"FCL/SDK\",\n                subject: \"The built-in default compute limit (DEFAULT_COMPUTE_LIMIT=10)\",\n                transition: \"https://github.com/onflow/flow-js-sdk/blob/master/packages/sdk/TRANSITIONS.md#0009-deprecate-default-compute-limit\",\n            });\n            ix.message.computeLimit = DEFAULT_COMPUTE_LIMIT;\n        }\n    }\n    return ix;\n}\n", "import { sansPrefix } from \"@onflow/util-address\";\nimport { makeGetAccount, Ok, pipe, } from \"../interaction/interaction.js\";\n/**\n * @description A builder function that returns the interaction to get an account by address\n * @param addr The address of the account to get\n * @returns A function that processes an interaction object\n */\nexport function getAccount(addr) {\n    return pipe([\n        makeGetAccount,\n        ix => {\n            ix.account.addr = sansPrefix(addr);\n            return Ok(ix);\n        },\n    ]);\n}\n", "import { sansPrefix } from \"@onflow/util-address\";\nexport async function resolveFinalNormalization(ix) {\n    for (let key of Object.keys(ix.accounts)) {\n        ix.accounts[key].addr = sansPrefix(ix.accounts[key].addr);\n    }\n    return ix;\n}\n", "import { get, pipe, Ok, Bad } from \"../interaction/interaction.js\";\nexport async function resolveValidators(ix) {\n    const validators = get(ix, \"ix.validators\", []);\n    return pipe(ix, validators.map((cb) => (ix) => cb(ix, { Ok, Bad })));\n}\n", "import { get, isFn } from \"../interaction/interaction.js\";\nimport { createSignableVoucher } from \"./voucher.js\";\nexport async function resolveVoucherIntercept(ix) {\n    const fn = get(ix, \"ix.voucher-intercept\");\n    if (isFn(fn)) {\n        await fn(createSignableVoucher(ix));\n    }\n    return ix;\n}\n", "export class SubscriptionsNotSupportedError extends Error {\n    constructor() {\n        super(`The current transport does not support subscriptions.  If you have provided a custom transport (e.g. via \\`sdk.transport\\` configuration), ensure that it implements the subscribe method.`);\n        this.name = \"SubscriptionsNotSupportedError\";\n    }\n}\n", "import { config } from \"@onflow/config\";\nimport { httpTransport as defaultTransport } from \"@onflow/transport-http\";\nimport { invariant } from \"@onflow/util-invariant\";\nimport { SubscriptionsNotSupportedError } from \"./subscribe/errors.js\";\n/**\n * Get the SDK transport object, either from the provided override or from the global config.\n * @param overrides - Override default configuration with custom transport or send function.\n * @returns The SDK transport object.\n */\nexport async function getTransport(override = {}) {\n    invariant(override.send == null || override.transport == null, `SDK Transport Error: Cannot provide both \"transport\" and legacy \"send\" options.`);\n    const transportOrSend = override.transport ||\n        override.send ||\n        (await config().first([\"sdk.transport\", \"sdk.send\"], defaultTransport));\n    // Backwards compatibility with legacy send function\n    if (!isTransportObject(transportOrSend)) {\n        return {\n            send: transportOrSend,\n            subscribe: () => {\n                throw new SubscriptionsNotSupportedError();\n            },\n        };\n    }\n    return transportOrSend;\n}\nfunction isTransportObject(transport) {\n    return (transport.send !== undefined &&\n        transport.subscribe !== undefined &&\n        typeof transport.send === \"function\" &&\n        typeof transport.subscribe === \"function\");\n}\n", "import { <PERSON><PERSON><PERSON> } from \"@onflow/rlp\";\nimport { initInteraction, pipe } from \"../../interaction/interaction.js\";\nimport * as ixModule from \"../../interaction/interaction.js\";\nimport { invariant } from \"../../build/build-invariant.js\";\nimport { response } from \"../../response/response.js\";\nimport { config } from \"@onflow/config\";\nimport { resolve as defaultResolve } from \"../../resolve/resolve.js\";\nimport { getTransport } from \"../get-transport.js\";\n/**\n * @description - Sends arbitrary scripts, transactions, and requests to Flow\n * @param args - An array of functions that take interaction and return interaction\n * @param opts - Optional parameters\n * @returns - A promise that resolves to a response\n */\nexport const send = async (args = [], opts = {}) => {\n    const transport = await getTransport(opts);\n    const sendFn = transport.send.bind(transport);\n    invariant(sendFn, `Required value for sdk.transport is not defined in config. See: ${\"https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21\"}`);\n    const resolveFn = await config.first([\"sdk.resolve\"], opts.resolve || defaultResolve);\n    opts.node = opts.node || (await config().get(\"accessNode.api\"));\n    if (Array.isArray(args))\n        args = pipe(initInteraction(), args);\n    return sendFn(await resolveFn(args), { config, response, ix: ixModule, Buffer }, opts);\n};\n", "/**\n * @description A builder function that sets the compute limit for a transaction\n * @param limit The compute limit to set\n * @returns A function that processes an interaction object\n */\nexport function limit(limit) {\n    return ix => {\n        ix.message.computeLimit = limit;\n        return ix;\n    };\n}\n"], "names": ["ROLES", "ACCT", "InteractionResolverKind", "ACCOUNT", "ARG", "ARGUMENT", "IX", "InteractionTag", "UNKNOWN", "InteractionStatus", "OK", "KEYS", "Set", "Object", "keys", "JSON", "parse", "initInteraction", "isNumber", "d", "isArray", "Array", "isObj", "isNull", "isFn", "isInteraction", "ix", "key", "hasOwnProperty", "Ok", "status", "Bad", "reason", "BAD", "makeIx", "wat", "tag", "prepAccountKeyId", "acct", "keyId", "invariant", "isNaN", "parseInt", "toString", "initAccount", "makeUnknown", "makeScript", "SCRIPT", "makeTransaction", "TRANSACTION", "makeGetTransactionStatus", "GET_TRANSACTION_STATUS", "makeGetTransaction", "GET_TRANSACTION", "makeGetAccount", "GET_ACCOUNT", "makeGetEvents", "GET_EVENTS", "makePing", "PING", "makeGetBlock", "GET_BLOCK", "makeGetBlockHeader", "GET_BLOCK_HEADER", "makeGetCollection", "GET_COLLECTION", "makeGetNetworkParameters", "GET_NETWORK_PARAMETERS", "makeSubscribeEvents", "SUBSCRIBE_EVENTS", "makeGetNodeVerionInfo", "GET_NODE_VERSION_INFO", "is", "isUnknown", "isScript", "isTransaction", "isGetTransactionStatus", "isGetTransaction", "isGetAccount", "isGetEvents", "isPing", "isGetBlock", "isGetBlockHeader", "isGetCollection", "isGetNetworkParameters", "isGetNodeVersionInfo", "isSubscribeEvents", "isBad", "rec<PERSON><PERSON>e", "async", "fns", "has", "Error", "hardMode", "length", "hd", "rest", "cur", "e", "pipe", "args", "arg1", "arg2", "identity", "v", "_", "get", "fallback", "undefined", "assigns", "build", "log", "deprecate", "pkg", "message", "transition", "level", "LEVELS", "warn", "account", "kind", "argument", "arg", "tempId", "uuidv4", "arguments", "push", "value", "asArgument", "xform", "resolve", "resolveArgument", "bind", "opts", "role", "authorization", "reduce", "fn", "accounts", "TransactionRole", "AUTHORIZER", "authorizations", "PAYER", "payer", "DEFAULT_RESPONSE", "transaction", "transactionStatus", "transactionId", "encodedData", "events", "event", "accountStatusEvent", "block", "blockHeader", "blockDigest", "latestBlock", "collection", "networkParameters", "streamConnection", "heartbeat", "nodeVersionInfo", "response", "getBlock", "isSealed", "decodeStream", "stream", "decodeResponse", "customDecoders", "newStream", "EventEmitter", "queue", "running", "run", "task", "shift", "taskQueue", "relayEvent", "on", "emit", "data", "topics", "filter", "newDataPromise", "Promise", "all", "map", "channel", "partialResponse", "for<PERSON>ach", "callback", "this", "off", "close", "decodeImplicit", "i", "decodeComposite", "composite", "decoders", "stack", "decoded", "fields", "acc", "name", "recurseDecode", "decoder", "id", "decoder<PERSON><PERSON><PERSON>", "defaultDecoders", "UInt", "Int", "UInt8", "Int8", "UInt16", "Int16", "UInt32", "Int32", "UInt64", "Int64", "UInt128", "Int128", "UInt256", "Int256", "Word8", "Word16", "Word32", "Word64", "Word128", "Word256", "UFix64", "Fix64", "String", "Character", "Bool", "Address", "Void", "Optional", "optional", "Reference", "array", "res", "type", "Dictionary", "dictionary", "Event", "Resource", "Struct", "Enum", "Type", "staticType", "Path", "Capability", "InclusiveRange", "range", "includes", "lookup", "found", "find", "test", "RegExp", "substring", "decodeInstructions", "join", "decode", "filteredDecoders", "customDecoder", "decoder<PERSON><PERSON>", "transactionIndex", "eventIndex", "payload", "blockId", "blockHeight", "blockTimestamp", "subject", "prefixRegex", "rawChainId", "chainId", "formattedChainId", "replace", "isString", "isOldIdentifierSyntax", "cadence", "isNewIdentifierSyntax", "cast", "handleArgResolution", "depth", "leftPaddedHexBuffer", "pad", "<PERSON><PERSON><PERSON>", "from", "padStart", "TRANSACTION_DOMAIN_TAG", "padEnd", "prependTransactionDomainTag", "tx", "addressBuffer", "addr", "argumentToString", "stringify", "rlpEncode", "encode", "preparePayload", "validatePayload", "script", "refBlock", "computeLimit", "sansPrefix", "<PERSON><PERSON><PERSON>", "address", "sequenceNum", "authorizers", "authorizer", "prepareEnvelope", "validateEnvelope", "preparePayloadSignatures", "signers", "collectSigners", "payloadSigs", "sig", "signerIndex", "sort", "a", "b", "signature", "Map", "addSigner", "set", "payloadFields", "field", "checkField", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "payloadSigsFields", "index", "payloadSigFields", "isObject", "check", "defaultVal", "obj", "base", "missing<PERSON>ieldE<PERSON>r", "<PERSON><PERSON><PERSON><PERSON><PERSON>r", "printFieldName", "findInsideSigners", "inside", "proposer", "add", "p", "delete", "findOutsideSigners", "outside", "createSignableVoucher", "withPrefix", "cid", "prev", "current", "item", "envelopeSigs", "recurseFlatMap", "el", "depthLimit", "flatMap", "addAccountToIx", "newAccount", "existingAccount", "uniqueAccountsFlatMap", "flatMapped", "seen", "accountId", "ids", "genAccountId", "param", "resolveSingleAccount", "currentAccountTempId", "debugLogger", "Math", "max", "prototype", "call", "Function", "accountWithoutResolve", "resolvedAccounts", "f_type", "f_vsn", "roles", "interaction", "voucher", "error", "console", "buildPreSignable", "flatResolvedAccounts", "flatResolvedAccount", "getAccountTempIDs", "rawTempIds", "replaceRoles", "oldAccountTempId", "newAccounts", "values", "AUTHORIZATIONS", "acctTempId", "concat", "x", "PROPOSER", "proposerAccts", "resolveAccountsByIds", "accountTempIds", "payerAddress", "newTempIds", "Boolean", "resolvedAccountTempIds", "foundNewAccounts", "resolvedAccountTempId", "payerTempID", "pAcct", "fetchSignature", "signingFunction", "buildSignable", "prepForEncoding", "noop", "debug", "config", "msg", "where", "then", "entries", "regex", "fullMatch", "contractName", "matchAll", "getContractIdentifierSyntaxMatches", "logger", "title", "getDebugMessage", "DEBUG_MESSAGE", "indent", "fill", "curr", "frontier", "size", "payerTempIds", "authorizersTempIds", "proposerTempIds", "ixAccount<PERSON><PERSON>s", "uniqueTempIds", "ixAccount<PERSON>ey", "removeUnusedIxAccounts", "enableDebug", "accts", "node", "sendFn", "first", "defaultSend", "ixModule", "acctResponse", "sequenceNumber", "insideSigners", "insidePayload", "outsideSigners", "outsidePayload", "encodeOutsideMessage", "cb", "SubscriptionsNotSupportedError", "constructor", "super", "getTransport", "override", "send", "transport", "transportOrSend", "defaultTransport", "subscribe", "resolveFn", "defaultResolve", "limit"], "mappings": "cAaIA;;;;;;;;;;;;;;;;;;;;;;ACTJ,MAAMC,OAAO,gBACDC,wBAAwBC,sQAe9BC,MAAM,gBACAF,wBAAwBG,0IAQ9BC,KAAK,eACAC,eAAeC,2CAEZC,kBAAkBC,6yBA+C1BC,OAAO,IAAIC,IAAIC,OAAOC,KAAKC,KAAKC,MAAMV,OAC/BW,kBAAkB,MAAMF,KAAKC,MAAMV,KAanCY,aAAYC,KAAmB,mBAANA,GACzBC,YAAWD,KAAME,MAAMD,QAAQD,IAC/BG,QAASH,KAAY,SAANA,KAA2B,mBAANA,GACpCI,SAAUJ,KAAW,QAALA,GAChBK,SAAQL,KAAmB,qBAANA,GACrBM,gBAAiBC;EAC1B,KAAKJ,MAAMI,OAAOH,OAAOG,OAAOR,WAASQ,KACrC,QAAO;EACX,KAAK,IAAIC,OAAOhB,MACZ,KAAKe,GAAGE,eAAeD,MACnB,QAAO;EACf,QAAO;GAEEE,KAAMH,OACfA,GAAGI,SAASrB,kBAAkBC,IACvBgB,KAEEK,MAAM,CAACL,IAAIM,YACpBN,GAAGI,SAASrB,kBAAkBwB;AAC9BP,GAAGM,SAASA,QACLN,KAELQ,SAAUC,OAAST,OACrBA,GAAGU,MAAMD,KACFN,GAAGH,MAERW,mBAAoBC,QACJ,QAAdA,KAAKC,QACED,QACXE,WAAWC,MAAMC,SAASJ,KAAKC,MAAMI,cAAc;AAC5C;KACAL;EACHC,OAAOG,SAASJ,KAAKC,MAAMI;IAGtBC,cAAc,MAAM7B,KAAKC,MAAMf,OAoD/B4C,cAAoCX,OAAO3B,eAAeC,UAC1DsC,aAAoCZ,OAAO3B,eAAewC,SAC1DC,kBAAoCd,OAAO3B,eAAe0C,cAC1DC,2BAAoChB,OAAO3B,eAAe4C,yBAC1DC,qBAAoClB,OAAO3B,eAAe8C,kBAC1DC,iBAAoCpB,OAAO3B,eAAegD,cAC1DC,gBAAoCtB,OAAO3B,eAAekD,aAC1DC,WAAoCxB,OAAO3B,eAAeoD,OAC1DC,eAAoC1B,OAAO3B,eAAesD,YAC1DC,qBAAoC5B,OAAO3B,eAAewD,mBAC1DC,oBAAoC9B,OAAO3B,eAAe0D,iBAC1DC,2BAAoChC,OAAO3B,eAAe4D,yBAC1DC,sBAAoClC,OAAO3B,eAAe8D,mBAC1DC,wBAAoCpC,OAAO3B,eAAegE,wBACjEC,KAAMrC,OAAST,MAAOA,GAAGU,QAAQD,KAC1BsC,YAAkCD,GAAGjE,eAAeC,UACpDkE,WAAkCF,GAAGjE,eAAewC,SACpD4B,gBAAkCH,GAAGjE,eAAe0C,cACpD2B,yBAAkCJ,GAAGjE,eAAe4C,yBACpD0B,mBAAkCL,GAAGjE,eAAe8C,kBACpDyB,eAAkCN,GAAGjE,eAAegD,cACpDwB,cAAkCP,GAAGjE,eAAekD,aACpDuB,SAAkCR,GAAGjE,eAAeoD,OACpDsB,aAAkCT,GAAGjE,eAAesD,YACpDqB,mBAAkCV,GAAGjE,eAAewD,mBACpDoB,kBAAkCX,GAAGjE,eAAe0D,iBACpDmB,yBAAkCZ,GAAGjE,eAAe4D,yBACpDkB,uBAAkCb,GAAGjE,eAAegE,wBACpDe,oBAAkCd,GAAGjE,eAAe8D,mBAEpDkB,QAAe7D,MAAOA,GAAGI,WAAWrB,kBAAkBwB,KAW7DuD,UAAUC,OAAO/D,IAAIgE,MAAM;EAC7B;IAEI,IADAhE,KATS,CAACA;MACd,KAAK,IAAIC,OAAOd,OAAOC,KAAKY,KACxB,KAAKf,KAAKgF,IAAIhE,MACV,MAAM,IAAIiE,MAAM,IAAIjE;MAE5B,OAAOD;MAIEmE,OAAenE,KAChB6D,MAAM7D,KACN,MAAM,IAAIkE,MAAM,sBAAsBlE,GAAGM;IAC7C,KAAK0D,IAAII,QACL,OAAOpE;IACX,OAAOqE,OAAOC,QAAQN,KAChBO,YAAYF;IAClB,IAAIvE,OAAKyE,MACL,OAAOT,QAAQS,IAAIvE,KAAKsE;IAC5B,IAAIzE,OAAO0E,SAASA,KAChB,OAAOT,QAAQ9D,IAAIsE;IACvB,IAAIvE,cAAcwE,MACd,OAAOT,QAAQS,KAAKD;IACxB,MAAM,IAAIJ,MAAM;AACpB,IACA,OAAOM;IACH,MAAMA;AACV;;;AAEJ,SAASC,QAAQC;EACb,OAAOC,MAAMC,QAAQF;EACrB,IAAIhF,UAAQiF,OACR,OAAQlF,KAAMgF,KAAKhF,GAAGkF;EAG1B,OAAOb,QAFIa,MACCC;AAEhB;;AAEA,MAAMC,WAAW,CAACC,MAAMC,MAAMD,GACjBE,MAAM,CAAChF,IAAIC,KAAKgF,gBAAWC,MACV,QAAnBlF,GAAGmF,QAAQlF,OAAegF,WAAWjF,GAAGmF,QAAQlF;;ACvPpD,SAASmF,MAAMpB,MAAM;EACxB,OAAOS,KAAKlF,mBAAmByE;AACnC;;;;;;WD+PwB/D,OAASD,cACtBA,GAAGmF,QAAQlF,MACXE,GAAGH;;;;eArLa,OACvBqF,IAAIC,UAAU;IACVC,KAAK;IACLC,SAAS;IACTC,YAAY;IACZC,OAAOC,OAAOC;MAEXrG;aAyHsBsG,WAAYA,QAAQC,SAAStH,wBAAwBC;cACrDsH,YAAaA,SAASD,SAAStH,wBAAwBG;;;;;;;;;;;;;;;;;QAJ5DqB,MAAOA,GAAGI,WAAWrB,kBAAkBC;;;;;;gBA3CtCgH,OAAShG;IAClC,IAAIiG,SAASC;IAWb,OAVAlG,GAAGwF,QAAQW,UAAUC,KAAKH,SAC1BjG,GAAGmG,UAAUF,UAAU5G,KAAKC,MAAMZ;IAClCsB,GAAGmG,UAAUF,QAAQA,SAASA,QAC9BjG,GAAGmG,UAAUF,QAAQI,QAAQL,IAAIK,OACjCrG,GAAGmG,UAAUF,QAAQK,aAAaN,IAAIM;IACtCtG,GAAGmG,UAAUF,QAAQM,QAAQP,IAAIO,OACjCvG,GAAGmG,UAAUF,QAAQO,UAAUR,IAAIQ;IACnCxG,GAAGmG,UAAUF,QAAQQ,kBAAkB3G,OAAKkG,IAAIS,mBAC1CT,IAAIS,gBAAgBC,KAAKV,OACzBA,IAAIS;IACHtG,GAAGH;;;;;;;;;;;;;;;;;eAjDa,CAACY,MAAM+F,OAAO,CAAE,MAAM3G;IAC7Cc,UAA0B,qBAATF,QAAuC,mBAATA,MAAmB;IAClEE,UAAuB,QAAb6F,KAAKC,MAAc;IAC7B,MAAMnI,UAAUyC,eACV0F,OAAOD,KAAKC,MACZX,SAASC;IACf,IAAIL,UAAU;SAAKjF;;IACfA,KAAKiG,iBAAiB/G,OAAKc,KAAKiG,mBAChChB,UAAU;MAAEW,SAAS5F,KAAKiG;SACzBjG,KAAKiG,iBAAiB/G,OAAKc,UAC5BiF,UAAU;MAAEW,SAAS5F;;IACzB,MAAM4F,UAAUX,QAAQW;IAwBxB,OAvBIA,YACAX,QAAQW,UAAU,CAAC5F,SAAS0D,SAAS,EAACkC,SAAS7F,mBAAkBmG,OAAO/C,OAAOtE,GAAGsH,OAAOA,SAAStH,MAAM6E,OAAO1D;IAEnHiF,UAAUlF,iBAAiBkF,UAC3B7F,GAAGgH,SAASf,UAAU;SACfxH;MACHwH;SACGJ;MACHe,MAAM;WACCnI,QAAQmI;WACc,mBAAdhG,KAAKgG,OAAoBhG,KAAKgG,OAAO,CAAE;WAC9CA,OAAO;UAAEA,CAACA,QAAO;YAAS,CAAA;;OAGlCA,SAASK,gBAAgBC,aACzBlH,GAAGmH,eAAef,KAAKH,UAElBW,SAASK,gBAAgBG,QAC9BpH,GAAGqH,MAAMjB,KAAKH,UAETW,SACL5G,GAAG4G,QAAQX;IAERjG;;OA2FQ,CAACC,KAAKoG,UAAWrG,OAChCA,GAAGmF,QAAQlF,OAAOoG,OACXlG,GAAGH;UAEQ,CAACC,KAAK8G,KAAKlC,aAAc7E,OAC3CA,GAAGmF,QAAQlF,OAAO8G,GAAG/G,GAAGmF,QAAQlF,MAAMD;EAC/BG,GAAGH;OAlDcA,MAAOA,GAAGM;;;AEnNtC,MAAMgH,mBAAmB;EACrB5G,KAAK;EACL6G,aAAa;EACbC,mBAAmB;EACnBC,eAAe;EACfC,aAAa;EACbC,QAAQ;EACRC,OAAO;EACPC,oBAAoB;EACpBhC,SAAS;EACTiC,OAAO;EACPC,aAAa;EACbC,aAAa;EACbC,aAAa;EACbC,YAAY;EACZC,mBAAmB;EACnBC,kBAAkB;EAClBC,WAAW;EACXC,iBAAiB;GAERC,WAAW,OAAO;KAAKjB;;;ACd7B,SAASkB,SAASC,WAAW;EAChC,OAAOhE,KAAK,EACRvC,cACAlC,OACIA,GAAG8H,MAAMW,WAAWA,UACbtI,GAAGH;AAGtB;;ACTO,MAAM0I,eAAe,CAACC,QAAQC,gBAAgBC;EACjD,MAAMC,YAAY,IAAIC;EACtB,IAAIC,QAkDR;IACI,IAAIA,QAAQ,IACRC,WAAU;IACdlF,eAAemF;MACX,KAAID,SAAJ;QAGA,KADAA,WAAU,GACHD,MAAM5E,SAAS,KAAG;UACrB,MAAM+E,OAAOH,MAAMI;iBACbD;AACV;QACAF,WAAU;AANN;AAOR;IACA,OAAO;MACH7C,MAAO+C;QACHH,MAAM5C,KAAK+C,OACXD;;;AAGZ,GArEgBG;EA2BZ,SAASC,WAAW1B;IAChBe,OAAOY,GAAG3B,OAAQpC;MACdwD,MAAM5C,KAAKrC;QACP+E,UAAUU,KAAK5B,OAAOpC;;;AAGlC;EAGA,OAhCAmD,OAAOY,GAAG,QAAQxF,MAAO0F;IACrB,MAAMC,SAASvK,OAAOC,KAAKqK,MAAME,OAAO1J,OAAoB,QAAbwJ,KAAKxJ,QAAwB,UAARA;IACpE,IAAI2J,iBAAiBC,QAAQC,IAAIJ,OAAOK,IAAIhG,MAAOiG;MAC/C,MAAMC,kBAAkB;QACpBD,CAACA,UAAUP,KAAKO;;MAGpB,OAAO;QACHA;QACAxE,eAHkBoD,eAAeqB,iBAAiBpB;;;IAM1DG,MAAM5C,KAAKrC;aAEe6F,gBACdM,QAAQ,EAAGF,kBAASxE;QACxBsD,UAAUU,KAAKQ,SAASxE;;;MAcpC8D,WAAW,UACXA,WAAW,UACJ;IACHC,EAAAA,CAAGS,SAASG;MAER,OADArB,UAAUS,GAAGS,SAASG,WACfC;AACX;IACAC,GAAAA,CAAIL,SAASG;MAET,OADArB,UAAUuB,IAAIL,SAASG,WAChBC;AACX;IACAE,OAAO;MACH3B,OAAO2B;;;;;ACnDnB,MAeMC,iBAAiBxG,MAAOyG,KAAMA,GAa9BC,kBAAkB1G,OAAO2G,WAAWC,UAAUC;EAChD,MAAMC,gBAAgBH,UAAUI,OAAOhE,OAAO/C,OAAOgH,KAAKjG,QACtDiG,YAAYA,KACRjG,EAAEkG,cAAcC,cAAcnG,EAAEuB,OAAOsE,UAAU,KAAIC,OAAO9F,EAAEkG;EAC3DD,MACRlB,QAAQrD,QAAQ,CAAA,KACb0E,UAAUR,UAAUS,MAAMC,cAAcT,UAAUD,UAAUS;EAClE,OAAOD,gBAAgBA,QAAQL,WAAWA;GAgBxCQ,kBAAkB;EACpBC,MAAMf;EACNgB,KAAKhB;EACLiB,OAAOjB;EACPkB,MAAMlB;EACNmB,QAAQnB;EACRoB,OAAOpB;EACPqB,QAAQrB;EACRsB,OAAOtB;EACPuB,QAAQvB;EACRwB,OAAOxB;EACPyB,SAASzB;EACT0B,QAAQ1B;EACR2B,SAAS3B;EACT4B,QAAQ5B;EACR6B,OAAO7B;EACP8B,QAAQ9B;EACR+B,QAAQ/B;EACRgC,QAAQhC;EACRiC,SAASjC;EACTkC,SAASlC;EACTmC,QAAQnC;EACRoC,OAAOpC;EACPqC,QAAQrC;EACRsC,WAAWtC;EACXuC,MAAMvC;EACNwC,SAASxC;EACTyC,MA9DejJ,YAAY;EA+D3BkJ,UA3DmBlJ,OAAOmJ,UAAUvC,UAAUC,UAAUsC,iBAAiBjC,cAAciC,UAAUvC,UAAUC,SAAS;EA4DpHuC,WAAW5C;EACX5K,OA5DgBoE,OAAOqJ,OAAOzC,UAAUC,gBAAgBf,QAAQC,IAAIsD,MAAMrD,IAAIjF,KAAK,IAAI+E,QAAQ9F,MAAOsJ,OAAQA,UAAUpC,cAAcnG,GAAG6F,UAAU,KAAIC,OAAO9F,EAAEwI;EA6DhKC,YA5DqBxJ,OAAOyJ,YAAY7C,UAAUC,gBAAgB4C,WAAW1G,OAAO/C,OAAOgH,KAAKjG,QAChGiG,YAAYA,WACFE,cAAcnG,EAAE7E,KAAK0K,UAAU,KAAIC,OAAO9F,EAAE7E,gBAC5CgL,cAAcnG,EAAEuB,OAAOsE,UAAU,KAAIC,OAAO9F,EAAE7E;EACjD8K,MACRlB,QAAQrD,QAAQ,CAAA;EAwDfiH,OAAOhD;EACPiD,UAAUjD;EACVkD,QAAQlD;EACRmD,MAAMnD;EACNoD,MAtEe9J,MAAOuJ,QACfA,KAAKQ;EAsEZC,MAAMxD;EACNyD,YAAYzD;EACZ0D,gBArDyBlK,OAAOmK,OAAOvD,UAAUC;IAIjD,MAAMxL,OAAO,EAAC,SAAS,OAAO;IAQ9B,aAPsBD,OAAOC,KAAK8O,OAAOpH,OAAO/C,OAAOgH,KAAK9K,SACxD8K,YAAYA,KACR3L,KAAK+O,SAASlO,SACd8K,IAAI9K,aAAagL,cAAciD,MAAMjO,MAAM0K,UAAU,KAAIC,OAAO3K;IAE7D8K,MACRlB,QAAQrD,QAAQ,CAAA;;GA4CjB4E,gBAAgB,CAACT,UAAUyD;EAC7B,MAAMC,QAAQlP,OAAOC,KAAKuL,UAAU2D,KAAKpD;IACrC,IAAI,WAAWqD,KAAKrD,UAAU;MAE1B,OADY,IAAIsD,OAAOtD,QAAQuD,UAAU,GAAGvD,QAAQ9G,SAAS,IAClDmK,KAAKH;AACpB;IACA,OAAOlD,YAAYkD;;EAEvB,OAAOA,UAAUC,SAAS1D,SAAS0D;GAEjCpD,gBAAgBlH,OAAO2K,oBAAoB/D,UAAUC;EACvD,IAAIM,UAAUE,cAAcT,UAAU+D,mBAAmBpB;EACzD,KAAKpC,SACD,MAAM,IAAIhH,MAAM,4BAA4BwK,mBAAmBpB,QAAQ1C,MAAM+D,KAAK;EACtF,aAAazD,QAAQwD,mBAAmBrI,OAAOsE,UAAUC;GAShDgE,SAAS7K,OAAO2K,oBAAoB7F,iBAAiB,CAAE,GAAE+B,QAAQ;EAE1E,MAAMiE,mBAAmB1P,OAAOC,KAAKiM,iBAChC1B,OAAOuB,YAAY/L,OAAOC,KAAKyJ,gBAAgByF,KAAKQ,iBAAiB,IAAIN,OAAOM,eAAeP,KAAKrD,WACpGpE,OAAO,CAAC6D,UAAUoE,gBACnBpE,SAASoE,cAAc1D,gBAAgB0D;EAChCpE,WACR9B,iBACG8B,WAAW;OACVkE;OACAhG;;EAEP,OAAOoC,cAAcyD,oBAAoB/D,UAAUC;GAE1ChC,iBAAiB7E,OAAOwE,UAAUM,iBAAiB;EAC5D,IAAIN,SAASb,aACT,OAAOkH,OAAOrG,SAASb,aAAamB;EAEnC,IAAIN,SAASf,mBACd,OAAO;OACAe,SAASf;IACZG,cAAckC,QAAQC,IAAIvB,SAASf,kBAAkBG,OAAOoC,IAAIhG,eAA4BS;MACxF,OAAO;QACH8I,MAAM9I,EAAE8I;QACR7F,eAAejD,EAAEiD;QACjBuH,kBAAkBxK,EAAEwK;QACpBC,YAAYzK,EAAEyK;QACdxF,YAAYmF,OAAOpK,EAAE0K,SAASrG;;AAEtC;;EAGH,IAAIN,SAAShB,aACd,OAAOgB,SAAShB;EAEf,IAAIgB,SAASZ,QACd,aAAakC,QAAQC,IAAIvB,SAASZ,OAAOoC,IAAIhG,eAA4BS;IACrE,OAAO;MACH2K,SAAS3K,EAAE2K;MACXC,aAAa5K,EAAE4K;MACfC,gBAAgB7K,EAAE6K;MAClB/B,MAAM9I,EAAE8I;MACR7F,eAAejD,EAAEiD;MACjBuH,kBAAkBxK,EAAEwK;MACpBC,YAAYzK,EAAEyK;MACdxF,YAAYmF,OAAOpK,EAAE0K,SAASrG;;AAEtC;EAEC,IAAIN,SAASX,OAAO;IACrB,OAAMsH,SAAEA,YAAY5K,QAASiE,SAASX;IACtC,OAAO;SACAtD;MACHmF,YAAYmF,OAAOM,SAASrG;;;EAG/B,IAAIN,SAASV,oBAAoB;IAClC,OAAMqH,SAAEA,YAAY5K,QAASiE,SAASV;IACtC,OAAO;SACAvD;MACHmF,YAAYmF,OAAOM,SAASrG;;;EAG/B,IAAIN,SAAS1C,SACd,OAAO0C,SAAS1C;EAEf,IAAI0C,SAAST,OACd,OAAOS,SAAST;EAEf,IAAIS,SAASR,aACd,OAAOQ,SAASR;EAEf,IAAIQ,SAASP,aACd,OAAOO,SAASP;EAEf,IAAIO,SAASN,aAEd,OA/LJ5C,IAAIC,UAAU;IACVC,KAAK;IACL+J,SAAS;IACT7J,YAAY;MA4LL8C,SAASN;EAEf,IAAIM,SAASd,eACd,OAAOc,SAASd;EAEf,IAAIc,SAASL,YACd,OAAOK,SAASL;EAEf,IAAIK,SAASJ,mBAAmB;IACjC,MAAMoH,cAAc,UACdC,aAAajH,SAASJ,kBAAkBsH;IAC9C,IAAIC;IAUJ,OARIA,mBADe,oBAAfF,aACmB,UAEdD,YAAYhB,KAAKiB,cACHA,WAAWG,QAAQJ,aAAa,MAGhCC;IAEhB;MACHC,SAASC;;;EAGZ,OAAInH,SAASH,mBACPM,aAAaH,SAASH,kBAAkBQ,gBAAgBC,kBAE1DN,SAASF,YACPE,SAASF,YAEXE,SAASD,kBACPC,SAASD,kBAEb;GC/NLxI,SAAQgF,KAAmB,qBAANA,GACrB8K,aAAY9K,KAAmB,mBAANA;;AAE/B,SAAS+K,sBAAsBC;EAC3B,OAFiC,eAEDvB,KAAKuB;AACzC;;AAEA,SAASC,sBAAsBD;EAC3B,OAFiC,oBAEDvB,KAAKuB;AACzC;;ACZA,MAAMhQ,SAAQgF,KAAmB,qBAANA;;AAC3B,SAASkL,KAAKhK;EAGV,OADAlF,UAA8B,eAAbkF,IAAIO,OAAe,mCAAmCP,IAAIK;EACvEvG,OAAKkG,IAAIO,SACFP,IAAIO,MAAMP,IAAIK,SACrBvG,OAAKkG,IAAIO,MAAMD,cACRN,IAAIO,MAAMD,WAAWN,IAAIK,cAEpCvF,WAAU,GAAO,oBAAoBkF;AACzC;;AACAjC,eAAekM,oBAAoBjK,KAAKkK,QAAQ;EAE5C,IADApP,UAAUoP,QAAQ,GAAG,sDAAsDlK,IAAIC;EAC3EnG,OAAKkG,IAAIS,kBAAkB;IAE3B,OAAOwJ,0BADmBjK,IAAIS,mBACUyJ,QAAQ;;EAGhD,OAAOlK;AAEf;;ACnBO,MAIDmK,sBAAsB,CAAC9J,OAAO+J,QAAQC,OAAOC,KAAKjK,MAAMkK,SAAe,IAANH,KAAS,MAAM,QAChFI,0BAFwBnK,QAEsBgK,OAAOC,KAAK,yBAAyBrP,SAAS;AAF7DmP,MAEqE,IAF7DC,OAAOC,KAAKjK,MAAMoK,OAAa,IAANL,KAAS,MAAM,QAEyBnP,SAAS;;AAF1F,IAACoF,OAAO+J;;AAGrC,MAAMM,8BAA+BC,MAAOH,yBAAyBG,IAC/DC,gBAAiBC,QAASV,oBAAoBU,MAAM,IAEpDC,mBAAoB9K,OAAQqK,OAAOC,KAAKjR,KAAK0R,UAAU/K,MAAM,SAG7DgL,YAAalM,KACRmM,OAAOnM,GAAG7D,SAAS,QAOxBiQ,iBAAkBP;EAEpB,OADAQ,gBAAgBR,KACT,GAZWS,SAaDT,GAAGb,WAAW,IAbFO,OAAOC,KAAKc,QAAQ,UAc7CT,GAAGxK,UAAU4D,IAAI+G,oBAhBJhJ,QAiBD6I,GAAGU,YAAY;EAjBJlB,oBAAoBrI,OAAO,MAkBlD6I,GAAGW,cACHV,cAAcW,WAAWZ,GAAGa,YAAYC,WAAW,MACnDd,GAAGa,YAAY3Q,OACf8P,GAAGa,YAAYE,aACfd,cAAcW,WAAWZ,GAAGtJ,SAC5BsJ,GAAGgB,YAAY5H,IAAI6H,cAAchB,cAAcW,WAAWK;EAvB9C,IAAC9J,OAECsJ;GAwBhBS,kBAAmBlB,OACrBmB,iBAAiBnB,KACV,EAACO,eAAeP,KAAKoB,yBAAyBpB,QAEnDoB,2BAA4BpB;EAC9B,MAAMqB,UAAUC,eAAetB;EAC/B,OAAOA,GAAGuB,aACJnI,IAAKoI,QACA;IACHC,aAAaJ,QAAQhN,IAAIuM,WAAWY,IAAIV,aAAa;IACrD5Q,OAAOsR,IAAItR;IACXsR,KAAKA,IAAIA;MAGZE,KAAK,CAACC,GAAGC,MACND,EAAEF,cAAcG,EAAEH,cACX,IACPE,EAAEF,cAAcG,EAAEH,eACV,IACRE,EAAEzR,QAAQ0R,EAAE1R,QACL,IACPyR,EAAEzR,QAAQ0R,EAAE1R,SACJ,IACL,GAENkJ,IAAIoI;IACL,OAAO,EAACA,IAAIC,aAAaD,IAAItR,QAjDZ2R,YAiDmCL,IAAIA,KAjDzB9B,OAAOC,KAAKkC,WAAW;IAAtC,IAACA;;GAoDnBP,iBAAkBtB;EACpB,MAAMqB,UAAU,IAAIS;EACpB,IAAIjI,IAAI;EACR,MAAMkI,YAAa7B;IACVmB,QAAQ/N,IAAI4M,UACbmB,QAAQW,IAAI9B,MAAMrG,IAClBA;;EAQR,OALImG,GAAGa,YAAYC,WACfiB,UAAU/B,GAAGa,YAAYC,UAE7BiB,UAAU/B,GAAGtJ;EACbsJ,GAAGgB,YAAYzH,QAAQwI,YAChBV;GAyCLb,kBAAmBR;EACrBiC,cAAc1I,QAAQ2I,SAASC,WAAWnC,IAAIkC,SAC9CE,kBAAkB7I,QAAQ2I,SAASC,WAAWnC,GAAGa,aAAaqB,OAAO;GAEnEf,mBAAoBnB;EACtBqC,kBAAkB9I,QAAQ2I,SAASC,WAAWnC,IAAIkC,SAClDlC,GAAGuB,aAAahI,QAAQ,CAACiI,KAAKc;IAC1BC,iBAAiBhJ,QAAQ2I,SAASC,WAAWX,KAAKU,OAAO,eAAeI;;GAe1EzT,WAAYsF,KAAmB,mBAANA,GACzB8K,WAAY9K,KAAmB,mBAANA,GACzBqO,WAAYrO,KAAY,SAANA,KAA2B,mBAANA,GACvCpF,UAAWoF,KAAMqO,SAASrO,MAAMA,aAAanF,OAC7CiT,gBAAgB,EAClB;EAAE5H,MAAM;EAAWoI,OAAOxD;GAC1B;EAAE5E,MAAM;EAAaoI,OAAO1T;GAC5B;EAAEsL,MAAM;EAAYoI,OAAOxD;EAAUyD,YAAY;GACjD;EAAErI,MAAM;EAAgBoI,OAAO5T;GAC/B;EAAEwL,MAAM;EAAeoI,OAAOD;GAC9B;EAAEnI,MAAM;EAASoI,OAAOxD;GACxB;EAAE5E,MAAM;EAAeoI,OAAO1T;KAE5BqT,oBAAoB,EACtB;EAAE/H,MAAM;EAAWoI,OAAOxD;GAC1B;EAAE5E,MAAM;EAASoI,OAAO5T;GACxB;EAAEwL,MAAM;EAAeoI,OAAO5T;KAE5BwT,oBAAoB,EACtB;EAAEhI,MAAM;EAAeoI,OAAO1T;KAE5BwT,mBAAmB,EACrB;EAAElI,MAAM;EAAWoI,OAAOxD;GAC1B;EAAE5E,MAAM;EAASoI,OAAO5T;GACxB;EAAEwL,MAAM;EAAOoI,OAAOxD;KAUpBkD,aAAa,CAACQ,KAAKT,OAAOU,MAAMN;EAClC,OAAMjI,MAAEA,MAAIoI,OAAEA,OAAKC,YAAEA,cAAeR;EAGpC,IAFiB,QAAbS,IAAItI,SAA+B,QAAdqI,eACrBC,IAAItI,QAAQqI,aACC,QAAbC,IAAItI,OACJ,MAAMwI,kBAAkBxI,MAAMuI,MAAMN;EACxC,KAAKG,MAAME,IAAItI,QACX,MAAMyI,kBAAkBzI,MAAMuI,MAAMN;GAEtCS,iBAAiB,CAACb,OAAOU,MAAMN,UAC3BM,OACc,QAATN,QAAgB,GAAGM,QAAQV,UAAU,GAAGU,QAAQN,SAASJ,UAC7DA,OAELW,oBAAoB,CAACX,OAAOU,MAAMN,UAAU,IAAI/O,MAAM,iBAAiBwP,eAAeb,OAAOU,MAAMN,WACnGQ,oBAAoB,CAACZ,OAAOU,MAAMN,UAAU,IAAI/O,MAAM,iBAAiBwP,eAAeb,OAAOU,MAAMN;;AC9LlG,SAASU,kBAAkB3T;EAE9B,IAAI4T,SAAS,IAAI1U,IAAIc,GAAGmH;EAUxB,OATInH,GAAG6T,YACHD,OAAOE,IAAI9T,GAAG6T,WAEdlU,MAAMD,QAAQM,GAAGqH,SACjBrH,GAAGqH,MAAM6C,QAAQ6J,KAAKH,OAAOI,OAAOD,MAGpCH,OAAOI,OAAOhU,GAAGqH;EAEd1H,MAAM2Q,KAAKsD;AACtB;;AACO,SAASK,mBAAmBjU;EAE/B,IAAIkU,UAAU,IAAIhV,IAAIS,MAAMD,QAAQM,GAAGqH,SAASrH,GAAGqH,QAAQ,EAACrH,GAAGqH;EAC/D,OAAO1H,MAAM2Q,KAAK4D;AACtB;;AACO,MAAMC,wBAAyBnU;EAClC,MAkBMwR,cAAcxR,GAAG6T,WACjB;IACEpC,SAAS2C,WAAWpU,GAAGgH,SAAShH,GAAG6T,UAAUhD;IAC7ChQ,OAAOb,GAAGgH,SAAShH,GAAG6T,UAAUhT;IAChC6Q,aAAa1R,GAAGgH,SAAShH,GAAG6T,UAAUnC;MAExC,CAAC;EACP,OAAO;IACH5B,SAAS9P,GAAGwF,QAAQsK;IACpBuB,UAAUrR,GAAGwF,QAAQ6L,YAAY;IACjCC,cAActR,GAAGwF,QAAQ8L;IACzBnL,WAAWnG,GAAGwF,QAAQW,UAAU4D,IAAIoB,MAAMnL,GAAGmG,UAAUgF,IAAI7E;IAC3DkL;IACAnK,OAAO+M,WAAWpU,GAAGgH,SAASrH,MAAMD,QAAQM,GAAGqH,SAASrH,GAAGqH,MAAM,KAAKrH,GAAGqH,OAAOwJ;IAChFc,aA/BuB3R,GAAGmH,eACrB4C,IAAIsK,OAAOD,WAAWpU,GAAGgH,SAASqN,KAAKxD,OACvC/J,OAAO,CAACwN,MAAMC,YACRD,KAAKhG,KAAKkG,QAAQA,SAASD,WAAWD,OAAO,KAAIA,MAAMC,WAC/D;IA4BHrC,aAzB6ByB,kBAAkB3T,IAAI+J,IAAIoB,OAAO;MAC9DsG,SAAS2C,WAAWpU,GAAGgH,SAASmE,IAAI0F;MACpChQ,OAAOb,GAAGgH,SAASmE,IAAItK;MACvBsR,KAAKnS,GAAGgH,SAASmE,IAAIqH;;IAuBrBiC,cArB8BR,mBAAmBjU,IAAI+J,IAAIoB,OAAO;MAChEsG,SAAS2C,WAAWpU,GAAGgH,SAASmE,IAAI0F;MACpChQ,OAAOb,GAAGgH,SAASmE,IAAItK;MACvBsR,KAAKnS,GAAGgH,SAASmE,IAAIqH;;;;;AVJ7B,SAASkC,eAAeC,IAAIC,aAAa;EACrC,OAAIA,cAAc,IACPD,KACNhV,MAAMD,QAAQiV,MAEZD,eAAeC,GAAGE,QAAQrQ,KAAKA,IAAIoQ,aAAa,KAD5CD;AAEf;;AAuCA,SAASG,eAAe9U,IAAI+U;EAxEf,IAACnU;EAyEqB,mBAApBmU,WAAWlE,QACW,mBAArBkE,WAAWlU,SACa,mBAArBkU,WAAWlU,QAItBkU,WAAW9O,SAAS8O,WAAW9O,UAAUC,OAHzC6O,WAAW9O,SA5EI,GAAGmO,YAAZxT,OA4EmBmU,YA5ESlE,SAASjQ,KAAKC;EAiFpD,MAAMmU,kBAAkBhV,GAAGgH,SAAS+N,WAAW9O,WAAW8O;EAU1D,OATK/U,GAAGgH,SAAS+N,WAAW9O,YACxBjG,GAAGgH,SAAS+N,WAAW9O,UAAU8O;EAErC/U,GAAGgH,SAAS+N,WAAW9O,QAAQW,KAAKiN,WAChCmB,gBAAgBpO,KAAKiN,YAAYkB,WAAWnO,KAAKiN;EACrD7T,GAAGgH,SAAS+N,WAAW9O,QAAQW,KAAKS,QAChC2N,gBAAgBpO,KAAKS,SAAS0N,WAAWnO,KAAKS;EAClDrH,GAAGgH,SAAS+N,WAAW9O,QAAQW,KAAKgL,aAChCoD,gBAAgBpO,KAAKgL,cAAcmD,WAAWnO,KAAKgL;EAChD5R,GAAGgH,SAAS+N,WAAW9O;AAClC;;AACA,SAASgP,sBAAsBjO;EAC3B,MAAMkO,aAAaR,eAAe1N,WAC5BmO,OAAO,IAAIjW;EAUjB,OATiCgW,WAC5BnL,IAAIlE;IACL,MAAMuP,YA7FO,KAAIC,QAAQA,IAAI1G,KAAK,KA6FhB2G,CAAazP,QAAQI,QAAQJ,QAAQe,KAAKS,OAAOxB,QAAQe,KAAKiN,UAAUhO,QAAQe,KAAKgL,YAAY/L,QAAQe,KAAK2O;IAChI,OAAIJ,KAAKlR,IAAImR,aACF,QACXD,KAAKrB,IAAIsB,YACFvP;KAEN8D,OAAOnF,KAAW,SAANA;AAErB;;AAEAT,eAAeyR,qBAAqBxV,IAAIyV,sBAAsBb,aA7GtC,IA6GoEc,aAAEA;EAC1F,IAAId,cAAc,GACd,MAAM,IAAI1Q,MAAM;EAEpB,IAAI2B,UAAU7F,GAAGgH,SAASyO;EAC1B,KAAK5P,SACD,OAAO,EAAC,KAAI;EAEhB,IADA6P,YAAY,YAAY7P,QAAQI,UAAU0P,KAAKC,IApH3B,IAoHiDhB,YAAY,KAC7E/O,SAASW,SAAS;IAClB,IApHM1B,IAoHGe,SAASW,SApHN1B,MACuB,wBAAtC3F,OAAO0W,UAAU5U,SAAS6U,KAAKhR,MAC5B,qBAAsBA,KACtBA,aAAaiR,WAiHe;MACxBL,YAAY,YAAY7P,QAAQI,wBAAwB0P,KAAKC,IAvHjD,IAuHuEhB,YAAY;MAC/F,OAAMpO,SAAEA,YAAYwP,yBAA0BnQ;MAC9C,IAAIoQ,yBAAyBzP,QAAQwP,uBAtF1C,SAA0BpV,MAAMZ;QACnC;UACI,OAAO;YACHkW,QAAQ;YACRC,OAAO;YACPC,OAAOxV,KAAKgG;YACZkJ,SAAS9P,GAAGwF,QAAQsK;YACpBpL,MAAM1E,GAAGwF,QAAQW,UAAU4D,IAAItK,KAAKO,GAAGmG,UAAU1G,GAAG6G;YACpDmD,MAAM,CAAC;YACP4M,aAAarW;YACbsW,SAASnC,sBAAsBnU;;AAEvC,UACA,OAAOuW;UAEH,MADAC,QAAQD,MAAM,oBAAoBA,QAC5BA;AACV;AACJ,OAqEwEE,CAAiBT,uBAAuBhW;MACpGiW,mBAAmBtW,MAAMD,QAAQuW,oBAC3BA,mBACA,EAACA;MACP,IAAIS,uBAAuBhC,eAAeuB;MAI1C,OAHAS,uBAAuBA,qBAAqB3M,IAAK4M,uBAAwB7B,eAAe9U,IAAI2W;MAC5F9Q,QAAQW,UAAUkQ,qBAAqB3M,IAAK4M,uBAAwBA,oBAAoB1Q;MACxFJ,UAAUiP,eAAe9U,IAAI6F,UACtB,EACH6Q,qBAAqB3M,IAAK4M,uBAAwBA,oBAAoB1Q,UACtE;;IAKJ,OADAyP,YAAY,YAAY7P,QAAQI,uBAAuB0P,KAAKC,IAvIhD,IAuIsEhB,YAAY;IACvF,EAAC/O,QAAQW,UAAS;AAEjC;EAxIS,IAAC1B;EAyIV,OAAO,EAACe,QAAQI,SAAS,EAACJ,QAAQI,WAAU,KAAI;AACpD;;CApIC,SAAU3H;EACPA,cAAiB,SACjBA,iBAAoB,YACpBA,uBAA0B;AAC9B,CAJC,CAIEA,UAAUA,QAAQ,CAAA;;AAiIrB,MAAMsY,oBAAqBC,cACJ,SAAfA,aACO,KAEJlX,MAAMD,QAAQmX,cAAcA,aAAa,EAACA;;AAErD9S,eAAe+S,aAAa9W,IAAI+W,kBAAkBC;EAG9C,KAAK,IAAIpQ,QAAQzH,OAAO8X,OAAO3Y,QAC3B,IAAIsI,SAAStI,MAAM4Y,kBAAkBtQ,SAAStI,MAAM8I,OAChDpH,GAAG4G,QAAQgQ,kBAAkB5W,GAAG4G,OAAOE,OAAO,CAACiE,KAAKoM,eAC5CA,eAAeJ,mBACRhM,IAAIqM,UAAUJ,YAChBrN,OAAO0N,KACAzQ,SAAUtI,MAAM8I,SAASiQ,EAAEzQ,KAAKS,SACnCT,SAAStI,MAAM4Y,kBAAkBG,EAAEzQ,KAAKgL,YAE5C7H,IAAInJ,QAAQA,KAAKqF,WAEnB8E,IAAIqM,OAAOD,aACnB,UAEF,IAAIvQ,SAAStI,MAAMgZ,UAAU;IAC9B,MAAMC,gBAAgBP,YAAYrN,OAAO0N,KAAKA,EAAEzQ,KAAKiN;IACrD,IAAI0D,cAAcnT,SAAS,GACvB,MAAM,IAAIF,MAAM;IAEpBlE,GAAG4G,QAAQ2Q,cAAc,IAAItR,UAAUjG,GAAG4G;AAC9C;AAER;;AACA7C,eAAeyT,qBAAqBxX,IAAIyX,gBAAgB7C,aA7KhC,IA6K8Dc,aAAEA;EACpF5U,UAAUd,MAAoB,mBAAPA,IAAiB;EACxC,IAkBI0X,cAlBAC,aAAa,IAAIzY;EACrB,KAAK,IAAIkW,aAAaqC,gBAAgB;IAClC,IAAI5R,UAAU7F,GAAGgH,SAASoO;IAC1BtU,UAAU8W,QAAQ/R,UAAU;IAC5B,OAAOgS,wBAAwBC,0BAA0BtC,qBAAqBxV,IAAIoV,WAAWR,YAAY;MACrGc;;IAGJ,IAAIoC,kBAAkB;MAClB,MACMpB,uBAAuBzB,sBADJ4C,uBAAuB9N,IAAKgO,yBAA0B/X,GAAGgH,SAAS+Q;MAG3FrB,qBAAqBxM,QAAQmN,KAAKM,WAAW7D,IAAIuD,EAAEpR,UAEnD6Q,aAAa9W,IAAIoV,WAAWsB;AAChC;AACJ;EAGA,KAAK,MAAMsB,eAAehY,GAAG1B,MAAM8I,QAAQ;IACvC,IAAI6Q,QAAQjY,GAAGgH,SAASgR;IACxB,IAAKN;MAEA,IAAIA,iBAAiBO,MAAMpH,MAC5B,MAAM,IAAI3M,MAAM;WAFhBwT,eAAeO,MAAMpH;AAI7B;EACA,OAAO8G;AACX;;AWzKA,SAASO,eAAelY,IAAIkP;EACxB,OAAOnL,eAAmCoH;IACtC,MAAMvK,OAAOZ,GAAGgH,SAASmE;IACzB,IAAsB,QAAlBvK,KAAK4R,kBAAwCtN,MAAnBtE,KAAK4R,WAC/B;IACJ,OAAMA,WAAEA,mBAAoB5R,KAAKuX,gBAIlC,SAAuBvX,MAAM4E,SAASxF;MACzC;QACI,OAAO;UACHkW,QAAQ;UACRC,OAAO;UACP3Q;UACAqL,MAAMU,WAAW3Q,KAAKiQ;UACtBhQ,OAAOD,KAAKC;UACZuV,OAAOxV,KAAKgG;UACZkJ,SAAS9P,GAAGwF,QAAQsK;UACpBpL,MAAM1E,GAAGwF,QAAQW,UAAU4D,IAAItK,KAAKO,GAAGmG,UAAU1G,GAAG6G;UACpDmD,MAAM,CAAC;UACP4M,aAAarW;UACbsW,SAASnC,sBAAsBnU;;AAEvC,QACA,OAAOuW;QAEH,MADAC,QAAQD,MAAM,iBAAiBA,QACzBA;AACV;AACJ,KAxByD6B,CAAcxX,MAAMsO,SAASlP;IAC9EA,GAAGgH,SAASmE,IAAIqH,YAAYA;AAChC;AACJ;;AAsBA,SAAS6F,gBAAgBrY;EACrB,MAAM0X,eAAenG,YAAY5R,MAAMD,QAAQM,GAAGqH,SAASrH,GAAGgH,SAAShH,GAAGqH,MAAM,MAAMrH,GAAGgH,SAAShH,GAAGqH,QAChGwJ,QAAQ,KACPW,cAAcxR,GAAG6T,WACjB;IACEpC,SAASF,WAAWvR,GAAGgH,SAAShH,GAAG6T,UAAUhD,SAAS;IACtDhQ,OAAOb,GAAGgH,SAAShH,GAAG6T,UAAUhT,SAAS;IACzC6Q,aAAa1R,GAAGgH,SAAShH,GAAG6T,UAAUnC,eAAe;MAEvD,CAAC;EACP,OAAO;IACH5B,SAAS9P,GAAGwF,QAAQsK;IACpBuB,UAAUrR,GAAGwF,QAAQ6L;IACrBC,cAActR,GAAGwF,QAAQ8L;IACzBnL,WAAWnG,GAAGwF,QAAQW,UAAU4D,IAAIoB,MAAMnL,GAAGmG,UAAUgF,IAAI7E;IAC3DkL;IACAnK,OAAOqQ;IACP/F,aAAa3R,GAAGmH,eACX4C,IAAIsK,OAAO9C,WAAWvR,GAAGgH,SAASqN,KAAKxD,SAAS,IAChD/J,OAAO,CAACwN,MAAMC,YACRD,KAAKhG,KAAKkG,QAAQA,SAASD,WAAWD,OAAO,KAAIA,MAAMC,WAC/D;;AAEX;;AC1EA,MAAM+D,OAAQxT,KAAMA,GACdyT,QAAQ,CAACtY,KAAK8G,KAAKuR,SAASvU,MAAO/D,aAmB3BwY,OAAOxT,IAAI,SAAS/E,gBACpB8G,GAAG/G,IAJD,IAAIyY;EACZjC,QAAQnR,IAAI,SAASpF,iBAAiBwY,KAAK;GAXhCzY,MAAO,EAClB,eACA;EACI6T,UAAU7T,GAAG6T;EACb1M,gBAAgBnH,GAAGmH;EACnBE,OAAOrH,GAAGqH;GAEd,gBACArH,GAAGgH,WACL2C,OAAOiO,WAMF5X,KAEEwG,UAAU/B,KAAK,ELzBrBV,eAA8B/D;EACjC,KAAKiD,cAAcjD,QAAQgD,SAAShD,KAChC,OAAOA;EACX,IAAI8P,UAAU9K,IAAIhF,IAAI;EActB,IAbAc,UAAUhB,OAAKgQ,YAAYF,WAASE,UAAU;EAC1ChQ,OAAKgQ,aACLA,gBAAgBA,QAAQ,CAAA,KAC5BhP,UAAU8O,WAASE,UAAU;EAC7BhP,WAAW+O,sBAAsBC,aAAaC,sBAAsBD,UAAU;EAC1ED,sBAAsBC,aACtBA,gBAAgB0I,SACXE,MAAM,OACNC,KAAKlZ,KAAKN,OAAOyZ,QAAQnZ,GAAGqH,OAAO,CAACgJ,UAAU7P,KAAKoG;IACpD,MAAMwS,QAAQ,IAAIrK,OAAO,SAASvO,MAAM,QAAQ;IAChD,OAAO6P,QAAQH,QAAQkJ,OAAOxS;KAC/ByJ,YAEHC,sBAAsBD,UACtB,KAAK,OAAOgJ,WAAWC,iBArB/B,SAA4CjJ;IACxC,OAAOA,QAAQkJ,SALkB;AAMrC,GAmBgDC,CAAmCnJ,UAAU;IACjF,MAAM2B,gBAAgB+G,SAASxT,IAAI,oBAAoB+T;IACnDtH,UACA3B,UAAUA,QAAQH,QAAQmJ,WAAW,UAAUC,qBAAqB3E,WAAW3C,cAG/EyH,OAAO7T,IAAI;MACP8T,OAAO;MACP3T,SAAS,gDAAgDuT;MACzDrT,OAAOwT,OAAOvT,OAAOC;;AAGjC;EAIJ,OADA5F,GAAGwF,QAAQsK,UAAUA,SACd9P;AACX,GKRIuY,MAAM,WAAW,CAACvY,IAAIqF,QAAQA,IAAIrF,GAAGwF,QAAQsK,WCzC1C/L,eAAmC/D;EAatC,OAZIiD,cAAcjD,QACdA,GAAGwF,QAAQ8L,eACPtR,GAAGwF,QAAQ8L,sBAAuBkH,OAAOxT,IAAI;EAC5ChF,GAAGwF,QAAQ8L,iBACZ4H,OAAO7T,IAAIC,UAAU;IACjBC,KAAK;IACL+J,SAAS;IACT7J,YAAY;MAEhBzF,GAAGwF,QAAQ8L,eAXO,OAcnBtR;AACX,GD6BIuY,MAAM,iBAAiB,CAACvY,IAAIqF,QAAQA,IAAIrF,GAAGwF,QAAQ8L,gBJxBhDvN,eAAgC/D;EACnC,IAAIiD,cAAcjD,OAAOgD,SAAShD,KAC9B,KAAK,KAAKmL,IAAInF,QAAQ7G,OAAOyZ,QAAQ5Y,GAAGmG,YAAY;IAChD,MAAMkH,YAAY4C,oBAAoBjK;IACtChG,GAAGmG,UAAUgF,IAAI7E,aAAa0J,KAAK3C;AACvC;EAEJ,OAAOrN;AACX,GIkBIuY,MAAM,aAAa,CAACvY,IAAIqF,QAAQA,IAAIrF,GAAGwF,QAAQW,WAAWnG,GAAGwF,WZiK1DzB,eAA+B/D,IAAI2G,OAAO;EAC7C,IAAI1D,cAAcjD,KAAK;IACdL,MAAMD,QAAQM,GAAGqH,UAClBhC,IAAIC,UAAU;MACVC,KAAK;MACL+J,SAAS;MACT9J,SAAS;;IAGjB,KAAKkQ,aAAa0D,mBAxM1B;MACI,MAEMC,gBAAgB;MACtB,OAAO,EACH,SAAUZ,MAAM,IAAIa,SAAS;QACzBD,cAAcjT,KAAKzG,MAJI,IAIE2Z,QACpBC,KANC,KAOD5K,KAAK,OAAO8J;AACrB,SACA;QACI,OAAOY,cAAcvS,OAAO,CAACwN,MAAMkF,SAASlF,OAAO,OAAOkF;AAC9D;AAER,KA0L6CjB;IACrC;MAEI,IAAI3D,aAxNQ,GAyNR6E,WAAW,IAAIva,IAAI,KAChB0X,kBAAkB5W,GAAG1B,MAAM8I,YAC3BwP,kBAAkB5W,GAAG1B,MAAMgZ,eAC3BV,kBAAkB5W,GAAG1B,MAAM4Y;MAElC,MAAOuC,SAASC,OAAO,KAAG;QACtB,IAAI9E,cAAc,GACd,MAAM,IAAI1Q,MAAM;QAEpBuV,iBAAiBjC,qBAAqBxX,IAAIyZ,UAAU7E,YAAY;UAC5Dc;YAEJd;AACJ;YAjLZ7Q,eAAsC/D;QAClC,MAAM2Z,eAAeha,MAAMD,QAAQM,GAAGqH,SAASrH,GAAGqH,QAAQ,EAACrH,GAAGqH,SACxDuS,qBAAqBja,MAAMD,QAAQM,GAAGmH,kBACtCnH,GAAGmH,iBACH,EAACnH,GAAGmH,kBACJ0S,kBAAkC,SAAhB7Z,GAAG6T,WACrB,KACAlU,MAAMD,QAAQM,GAAG6T,YACb7T,GAAG6T,WACH,EAAC7T,GAAG6T,YACRiG,gBAAgB3a,OAAOC,KAAKY,GAAGgH,WAC/B+S,gBAAgB,KACf,IAAI7a,IAAIya,aAAavC,OAAOwC,oBAAoBC;QAEvD,KAAK,MAAMG,gBAAgBF,eAClBC,cAAczL,KAAKnD,MAAMA,OAAO6O,wBAC1Bha,GAAGgH,SAASgT;AAG/B,OA+JkBC,CAAuBja;MAE7B,KAAK,MAAM4G,QAAQzH,OAAO8X,OAAO3Y,QAC7BwC,UAAU8V,kBAAkB5W,GAAG4G,OAAOxC,SAAS,KAC3CwC,SAAStI,MAAM4Y,gBAAgB,mDAAmDtQ;MAEtFD,KAAKuT,eACL1D,QAAQ+B,MAAMa;AAEtB,MACA,OAAO7C;MAEH,MADAC,QAAQD,MAAM,yBAAyBA,OAAO,0BACxCA;AACV;AACJ;EACA,OAAOvW;AACX,GY1MIuY,MAAM,YAAY,CAACvY,IAAIqF,KAAK8U,UAAU9U,OAAO8U,MAAMna,OAUvD+D,eAA4B/D;EACxB,IAAIiD,cAAcjD,OAA8B,QAAvBA,GAAGwF,QAAQ6L,UAAkB;IAClD,MAAM+I,aAAa5B,SAASxT,IAAI,mBAC1BqV,eAAe7B,OAAO8B,MAAM,EAAC,iBAAiB,cAAaC;IACjEzZ,UAAUuZ,QAAQ;IAClBra,GAAGwF,QAAQ6L,kBAAkBgJ,OAAOjV,MAAM,EAACoD,eAAc;MAAEgQ;MAAQjQ;MAAU8H;MAAQrQ,IAAIwa;OAAY;MAAEJ;OAAQzB,KAAK/J,iBAASzD;AACjI;EACA,OAAOnL;AACX,GACA+D,eAAuC/D;EACnC,IAAIiD,cAAcjD,KAAK;IACnB,IAAIY,OAAOzB,OAAO8X,OAAOjX,GAAGgH,UAAUsH,KAAMgE,KAAMA,EAAE1L,KAAKiN;IAEzD,IADA/S,eAAmBoE,MAATtE,MAAoB,oCAC1BA,QAA4B,QAApBA,KAAK8Q,aAAqB;MAClC,MAAM0I,aAAa5B,SAASxT,IAAI,mBAC1BqV,eAAe7B,OAAO8B,MAAM,EAAC,iBAAiB,cAAaC;MACjEzZ,UAAUuZ,QAAQ;MAClBra,GAAGgH,SAASpG,KAAKqF,QAAQyL,oBAAoB2I,aAAajV,MAAM,GEvEjDyL,OFuE6DjQ,KAAKiQ;MEtElFpM,KAAK,EACR7C,gBACA5B,OACIA,GAAG6F,QAAQgL,OAAOU,WAAWV,OACtB1Q,GAAGH,aFkEgF;QAAEwY;QAAQjQ;QAAU8H;QAAQrQ,IAAIwa;SAAY;QAAEJ;SACnIzB,KAAK/J,gBACL+J,KAAM8B,gBAAiBA,aAAarb,MACpCuZ,KAAMvZ,QAASA,KAAKkP,KAAMrO,OAAQA,IAAIgT,UAAUrS,KAAKC,QACrD8X,KAAM1Y,OAAQA,IAAIya;AAC3B;AACJ;EE7EG,IAAoB7J;EF8EvB,OAAO7Q;AACX,GDlFO+D,eAAiC/D;EACpC,IAAIiD,cAAcjD,KACd;IACI,IAAI2a,gBAAgBhH,kBAAkB3T;IACtC,MAAM4a,iBFLuBjK,KEKa0H,gBAAgBrY,KFLtB0Q,4BAA4BM,UAAUE,eAAeP;UEYnF9G,QAAQC,IAAI6Q,cAAc5Q,IAAImO,eAAelY,IAAI4a;IACvD,IAAIC,iBAAiB5G,mBAAmBjU;IACxC,MAAM8a,iBFbuB,CAACnK,MAAOD,4BAA4BM,UAAUa,gBAAgBlB,MEapEoK,CAAqB;SACrC1C,gBAAgBrY;MACnBkS,aAAayI,cAAc5Q,IAAIoB,OAAO;QAClCsG,SAASzR,GAAGgH,SAASmE,IAAI0F,QAAQ;QACjChQ,OAAOb,GAAGgH,SAASmE,IAAItK,SAAS;QAChCsR,KAAKnS,GAAGgH,SAASmE,IAAIqH,aAAa;;;UASpC3I,QAAQC,IAAI+Q,eAAe9Q,IAAImO,eAAelY,IAAI8a;AAC5D,IACA,OAAOvE;IAEH,MADAC,QAAQD,MAAM,cAAcA,OAAO;MAAEvW;QAC/BuW;AACV;EFjCgC,IAAC5F;EEmCrC,OAAO3Q;AACX,GCgBIuY,MAAM,cAAc,CAACvY,IAAIqF,KAAK8U,UAAU9U,OAAO8U,MAAMna,OGtDlD+D,eAAyC/D;EAC5C,KAAK,IAAIC,OAAOd,OAAOC,KAAKY,GAAGgH,WAC3BhH,GAAGgH,SAAS/G,KAAK4Q,OAAOU,WAAWvR,GAAGgH,SAAS/G,KAAK4Q;EAExD,OAAO7Q;AACX,GCLO+D,eAAiC/D;EAEpC,OAAOyE,KAAKzE,IADOgF,IAAIhF,IAAI,iBAAiB,IACjB+J,IAAKiR,MAAQhb,MAAOgb,GAAGhb,IAAI;IAAEG;IAAIE;;AAChE,GCFO0D,eAAuC/D;EAC1C,MAAM+G,KAAK/B,IAAIhF,IAAI;EAInB,OAHIF,OAAKiH,aACCA,GAAGoN,sBAAsBnU,MAE5BA;AACX,GLmDIuY,MAAM,YAAY,CAACvY,IAAIqF,QAAQA,IAAIrF;;AM3DhC,MAAMib,uCAAuC/W;EAChD,WAAAgX;IACIC,MAAM;IACN/Q,KAAKY,OAAO;AAChB;;;ACKGjH,eAAeqX,aAAaC,WAAW;EAC1Cva,UAA2B,QAAjBua,SAASC,QAAsC,QAAtBD,SAASE,WAAmB;EAC/D,MAAMC,kBAAkBH,SAASE,aAC7BF,SAASC,cACF9C,SAAS8B,MAAM,EAAC,iBAAiB,cAAamB;EAEzD,YAW2BvW,OADJqW,YAVAC,iBAWLF,aACUpW,MAAxBqW,UAAUG,aACgB,qBAAnBH,UAAUD,QACc,qBAAxBC,UAAUG,YAbV;IACHJ,MAAME;IACNE,WAAW;MACP,MAAM,IAAIT;;MAIfO;EAEX,IAA2BD;AAD3B;;MCVaD,OAAOvX,OAAOW,OAAO,IAAIiC,OAAO,CAAA;EACzC,MAAM4U,kBAAkBH,aAAazU,OAC/B0T,SAASkB,UAAUD,KAAK5U,KAAK6U,YAE7BI,kBAAkBnD,OAAO8B,MAAM,EAAC,iBAAgB3T,KAAKH,WAAWoV;EAItE,OAHAjV,KAAKyT,OAAOzT,KAAKyT,cAAe5B,SAASxT,IAAI,mBACzCrF,MAAMD,QAAQgF,UACdA,OAAOD,KAAKlF,mBAAmBmF;EAC5B2V,aAAasB,UAAUjX,OAAO;IAAE8T;IAAQjQ;IAAUvI,IAAIwa;IAAUnK;KAAU1J;;;ACjB9E,SAASkV,MAAMA;EAClB,OAAO7b,OACHA,GAAGwF,QAAQ8L,eAAeuK,OACnB7b;AAEf;;"}