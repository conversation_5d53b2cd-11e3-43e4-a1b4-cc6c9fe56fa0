{"name": "@onflow/sdk-esm", "version": "1.9.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@onflow/sdk-esm", "version": "1.9.0", "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/config": "1.5.2", "@onflow/rlp": "1.2.3", "@onflow/transport-http": "1.13.0", "@onflow/typedefs": "1.6.0", "@onflow/types": "1.4.1", "@onflow/util-actor": "1.3.4", "@onflow/util-address": "1.2.3", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3", "@onflow/util-template": "1.2.3", "deepmerge": "^4.3.1", "events": "^3.3.0", "sha3": "^2.1.4", "uuid": "^9.0.1"}, "devDependencies": {"typescript": "^5.6.3"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "http://repo.huaweicloud.com/repository/npm/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==", "engines": {"node": ">=6.9.0"}}, "node_modules/@onflow/config": {"version": "1.5.2", "resolved": "https://repo.huaweicloud.com/repository/npm/@onflow/config/-/config-1.5.2.tgz", "integrity": "sha512-pz/qIG7LhvH3A+a0IJUygwsndFTQ2GoVdADC89WBj9fmR1z4izKL6j2P21xL4gZ39u0jeS5EFWSkdsBWq4yb0w==", "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/util-actor": "1.3.4", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3"}}, "node_modules/@onflow/rlp": {"version": "1.2.3", "resolved": "https://repo.huaweicloud.com/repository/npm/@onflow/rlp/-/rlp-1.2.3.tgz", "integrity": "sha512-Mm1jSzDhdTofMGhg3NtUD8uKntj7u1dSMr+Q4VwOw2YQhwGTGJrzsHc7qgkJxwDnjU0Ra8VQfqd54bZzX0R2aQ==", "dependencies": {"@babel/runtime": "^7.25.7", "buffer": "^6.0.3"}}, "node_modules/@onflow/transport-http": {"version": "1.13.0", "resolved": "https://repo.huaweicloud.com/repository/npm/@onflow/transport-http/-/transport-http-1.13.0.tgz", "integrity": "sha512-Gw8o3r9Dcneiz64rRtxQZB4nzewpXVdi6OxTyuSG5QSA/WpxDUPu9JjAbxtDBGm9Ul//LW9g4KRUmSX+a+3FeA==", "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/util-address": "1.2.3", "@onflow/util-invariant": "1.2.4", "@onflow/util-logger": "1.3.3", "@onflow/util-template": "1.2.3", "abort-controller": "^3.0.0", "buffer": "^6.0.3", "cross-fetch": "^4.0.0", "events": "^3.3.0", "isomorphic-ws": "^5.0.0", "ws": "^8.18.0"}}, "node_modules/@onflow/transport-http/node_modules/cross-fetch": {"version": "4.1.0", "license": "MIT", "dependencies": {"node-fetch": "^2.7.0"}}, "node_modules/@onflow/typedefs": {"version": "1.6.0", "resolved": "https://repo.huaweicloud.com/repository/npm/@onflow/typedefs/-/typedefs-1.6.0.tgz", "integrity": "sha512-2JxZ5ePSeeh8OPRTQjxpQoavmvsYtK05O+6KcFTFcJp4x+YkVOxl0o8OZl5ERHWW2J3BvutuVFEmP0AXuKxzPg==", "dependencies": {"@babel/runtime": "^7.25.7"}}, "node_modules/@onflow/types": {"version": "1.4.1", "resolved": "https://repo.huaweicloud.com/repository/npm/@onflow/types/-/types-1.4.1.tgz", "integrity": "sha512-oKKaNTPfb9OJos4C6RoK3sql9Bx8mi+8ytTc7wLJbjv+n6YUov2zRGkGsPzj2QxL2Xf48CLYtPNn7cBNr3v39w==", "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/util-logger": "1.3.3"}}, "node_modules/@onflow/util-actor": {"version": "1.3.4", "resolved": "https://repo.huaweicloud.com/repository/npm/@onflow/util-actor/-/util-actor-1.3.4.tgz", "integrity": "sha512-BQeFdy0obs2x+XTEkss7MjuasS7gCfIStwGsgpH0aG3siBu+IsMYPiDdrHOeYS2Jn/pSFXF5R85NYrnMvlDhlA==", "dependencies": {"@babel/runtime": "^7.25.7", "queue-microtask": "1.2.3"}}, "node_modules/@onflow/util-address": {"version": "1.2.3", "resolved": "https://repo.huaweicloud.com/repository/npm/@onflow/util-address/-/util-address-1.2.3.tgz", "integrity": "sha512-5u1pLQT6MmTlRQLv8zVJP/iOkgytvpzK+32nXcJ29XE0y7YI6GLrFfgKGBIRsiqiSLp7SU6XI5RukEJEblmwOw==", "dependencies": {"@babel/runtime": "^7.25.7"}}, "node_modules/@onflow/util-invariant": {"version": "1.2.4", "resolved": "https://repo.huaweicloud.com/repository/npm/@onflow/util-invariant/-/util-invariant-1.2.4.tgz", "integrity": "sha512-U4D30lrAxSgqTPQsIvC3gPDoXVxuhLS9TZk4WxEvNfcQrI6VYKvWRe4m/5mUrc4kpE+ntXZmnbs+DUM7oLlkcg==", "dependencies": {"@babel/runtime": "^7.25.7"}}, "node_modules/@onflow/util-logger": {"version": "1.3.3", "resolved": "https://repo.huaweicloud.com/repository/npm/@onflow/util-logger/-/util-logger-1.3.3.tgz", "integrity": "sha512-eivdbF7cKNjTL2nuvI3pjDavDDfTXRq4pJtJpkI8hJMz0XJb84o7D5CLPcDRId//1Kc/qoqM/driHz5A4J52Qw==", "dependencies": {"@babel/runtime": "^7.25.7"}, "peerDependencies": {"@onflow/util-config": ">1.1.1"}, "peerDependenciesMeta": {"@onflow/util-config": {"optional": true}}}, "node_modules/@onflow/util-template": {"version": "1.2.3", "resolved": "https://repo.huaweicloud.com/repository/npm/@onflow/util-template/-/util-template-1.2.3.tgz", "integrity": "sha512-yNF7mI5L1y6yJHL+HxmTgIdd/oF1HD/kzjzZgjOyAvk+mLXzML+sWkqRSoIYcETbQ0w6cdNg3xvzZgTLuLIK3A==", "dependencies": {"@babel/runtime": "^7.25.7", "@onflow/util-logger": "1.3.3"}}, "node_modules/abort-controller": {"version": "3.0.0", "resolved": "https://repo.huaweicloud.com/repository/npm/abort-controller/-/abort-controller-3.0.0.tgz", "integrity": "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://repo.huaweicloud.com/repository/npm/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/buffer": {"version": "6.0.3", "resolved": "https://repo.huaweicloud.com/repository/npm/buffer/-/buffer-6.0.3.tgz", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/deepmerge": {"version": "4.3.1", "resolved": "http://repo.huaweicloud.com/repository/npm/deepmerge/-/deepmerge-4.3.1.tgz", "integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==", "engines": {"node": ">=0.10.0"}}, "node_modules/event-target-shim": {"version": "5.0.1", "resolved": "https://repo.huaweicloud.com/repository/npm/event-target-shim/-/event-target-shim-5.0.1.tgz", "integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==", "engines": {"node": ">=6"}}, "node_modules/events": {"version": "3.3.0", "resolved": "https://repo.huaweicloud.com/repository/npm/events/-/events-3.3.0.tgz", "integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==", "engines": {"node": ">=0.8.x"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://repo.huaweicloud.com/repository/npm/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/isomorphic-ws": {"version": "5.0.0", "resolved": "https://repo.huaweicloud.com/repository/npm/isomorphic-ws/-/isomorphic-ws-5.0.0.tgz", "integrity": "sha512-muId7Zzn9ywDsyXgTIafTry2sV3nySZeUDe6YedVd1Hvuuep5AsIlqK+XefWpYTyJG5e503F2xIuT2lcU6rCSw==", "peerDependencies": {"ws": "*"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://repo.huaweicloud.com/repository/npm/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/sha3": {"version": "2.1.4", "resolved": "https://repo.huaweicloud.com/repository/npm/sha3/-/sha3-2.1.4.tgz", "integrity": "sha512-S8cNxbyb0UGUM2VhRD4Poe5N58gJnJsLJ5vC7FYWGUmGhcsj4++WaIOBFVDxlG0W3To6xBuiRh+i0Qp2oNCOtg==", "dependencies": {"buffer": "6.0.3"}}, "node_modules/typescript": {"version": "5.8.3", "resolved": "https://repo.huaweicloud.com/repository/npm/typescript/-/typescript-5.8.3.tgz", "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "dev": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/uuid": {"version": "9.0.1", "resolved": "https://repo.huaweicloud.com/repository/npm/uuid/-/uuid-9.0.1.tgz", "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/ws": {"version": "8.18.2", "resolved": "http://repo.huaweicloud.com/repository/npm/ws/-/ws-8.18.2.tgz", "integrity": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}}}