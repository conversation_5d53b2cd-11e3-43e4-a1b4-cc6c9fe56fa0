# Flow SDK ESM Build

这是 Flow SDK 的 ESM (ECMAScript Modules) 构建版本，从 `fcl-js/packages/sdk` 编译而来。

## 📁 项目结构

```
pkg/
├── dist/           # 编译后的 ESM JavaScript 文件
├── types/          # TypeScript 类型声明文件
├── package.json    # 包配置和构建脚本
├── tsconfig.json   # TypeScript 编译配置
├── fix-imports.js  # ESM 导入路径修复脚本
└── example.js      # 使用示例
```

## 🚀 构建命令

```bash
# 清理并构建 ESM 版本
npm run build:esm

# 仅清理输出目录
npm run clean
```

## 📦 构建过程

1. **TypeScript 编译**: 使用 `tsc` 将 TypeScript 源码编译为 ESM JavaScript
2. **导入路径修复**: 自动添加 `.js` 扩展名和 `/index.js` 路径
3. **版本号修复**: 自动设置正确的版本号

## 💡 使用方法

### ES Modules 导入

```javascript
// 完整导入
import * as sdk from './dist/sdk.js';

// 按需导入
import { build, script, args, arg, t } from './dist/sdk.js';

// 使用示例
const interaction = await sdk.build([
  sdk.script`
    pub fun main(name: String): String {
      return "Hello, ".concat(name).concat("!")
    }
  `,
  sdk.args([sdk.arg("Flow", sdk.t.String)])
]);
```

### 类型支持

```typescript
import * as sdk from './dist/sdk.js';
import type { Interaction } from './types/sdk.d.ts';

const interaction: Interaction = await sdk.build([
  sdk.script`pub fun main(): String { return "Hello!" }`
]);
```

## 🔧 技术细节

- **模块格式**: ESNext
- **目标版本**: ES2020
- **模块解析**: Node.js
- **类型声明**: 包含完整的 TypeScript 类型定义

## 📋 依赖项

所有必要的 @onflow 依赖项都已包含在 package.json 中：

- @onflow/config
- @onflow/rlp
- @onflow/transport-http
- @onflow/typedefs
- @onflow/types
- @onflow/util-* 系列工具包

## ✅ 验证

运行示例文件来验证构建是否正常工作：

```bash
node example.js
```

## 🛠️ 自定义构建

如果需要修改构建配置：

1. 编辑 `tsconfig.json` 调整 TypeScript 编译选项
2. 修改 `fix-imports.js` 调整后处理逻辑
3. 更新 `package.json` 中的构建脚本

## 📝 注意事项

- 确保 Node.js 版本支持 ES Modules (>= 14.x)
- 在使用时需要明确指定 `.js` 扩展名
- 类型声明文件位于 `types/` 目录中
