import { InteractionAccount } from "@onflow/typedefs";
import { AccountAuthorization } from "../interaction/interaction";
import { Voucher } from "../encode/encode";
interface SignableMessage {
    message: string;
    addr: string;
    keyId: number | string;
    roles: {
        proposer: boolean;
        authorizer: boolean;
        payer: boolean;
    };
    voucher: Voucher;
}
interface SigningResult {
    addr?: string;
    keyId?: number | string;
    signature: string;
}
type SigningFn = (signable?: SignableMessage) => SigningResult | Promise<SigningResult>;
export declare function authorizations(ax?: Array<AccountAuthorization>): import("../interaction/interaction").InteractionBuilderFn;
export declare function authorization(addr: string, signingFunction: SigningFn, keyId?: number | string, sequenceNum?: number): Partial<InteractionAccount>;
export {};
