import { InteractionBuilderFn } from "../interaction/interaction";
/**
 * @description A builder function that returns a partial interaction to query the latest block with the given finality state
 * @param isSealed Block finality state, defaults to latest executed block ("soft-finality"), set to true for sealed blocks ("hard-finality")
 * @returns A function that processes a partial interaction object
 */
export declare function atLatestBlock(isSealed?: boolean): InteractionBuilderFn;
