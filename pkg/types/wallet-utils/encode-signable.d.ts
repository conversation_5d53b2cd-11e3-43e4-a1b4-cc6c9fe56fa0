interface PayloadSig {
    address: string;
    keyId: number | string;
    sig: string;
    [key: string]: any;
}
export interface Voucher {
    authorizers: string[];
    proposalKey: {
        address: string;
        keyId?: number | string;
        sequenceNum?: number;
        [key: string]: any;
    };
    payer: string;
    cadence: string;
    refBlock: string;
    computeLimit: number;
    arguments: any[];
    payloadSigs: PayloadSig[];
}
interface Signable {
    voucher: Voucher;
}
export declare class UnableToDetermineMessageEncodingTypeForSignerAddress extends Error {
    constructor(signerAddress: string);
}
export declare const encodeMessageFromSignable: (signable: Signable, signerAddress: string) => string;
export {};
