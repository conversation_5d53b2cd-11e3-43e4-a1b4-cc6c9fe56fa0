import type { Account } from "@onflow/typedefs";
interface AccountQueryOptions {
    height?: number;
    id?: string;
    isSealed?: boolean;
}
/**
 * @description Returns the details of an account from their public address
 * @param address Address of the account
 * @param queryOptions Query parameters
 * @param queryOptions.height Block height to query
 * @param queryOptions.id Block ID to query
 * @param queryOptions.isSealed Block finality
 * @param opts Optional parameters
 * @returns A promise that resolves to an account response
 */
export declare function account(address: string, { height, id, isSealed }?: AccountQueryOptions, opts?: object): Promise<Account>;
export {};
