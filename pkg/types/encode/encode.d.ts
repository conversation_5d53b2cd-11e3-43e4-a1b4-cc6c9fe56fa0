export declare const encodeTransactionPayload: (tx: Transaction) => string;
export declare const encodeTransactionEnvelope: (tx: Transaction) => string;
export declare const encodeTxIdFromVoucher: (voucher: Voucher) => string;
interface VoucherArgument {
    type: string;
    value: string;
}
interface VoucherProposalKey {
    address: string;
    keyId: number | null;
    sequenceNum: number | null;
}
interface Sig {
    address: string;
    keyId: number | string;
    sig: string;
}
export interface TransactionProposalKey {
    address?: string;
    keyId?: number | string;
    sequenceNum?: number;
}
export interface Transaction {
    cadence: string;
    refBlock: string;
    computeLimit: number;
    arguments: VoucherArgument[];
    proposalKey: TransactionProposalKey;
    payer: string;
    authorizers: string[];
    payloadSigs?: Sig[];
    envelopeSigs?: TransactionProposalKey[];
}
export interface Voucher {
    cadence: string;
    refBlock: string;
    computeLimit: number;
    arguments: VoucherArgument[];
    proposalKey: VoucherProposalKey;
    payer: string;
    authorizers: string[];
    payloadSigs: Sig[];
    envelopeSigs: Sig[];
}
export {};
