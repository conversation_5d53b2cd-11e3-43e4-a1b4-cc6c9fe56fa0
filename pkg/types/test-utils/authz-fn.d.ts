import { InteractionAccount } from "@onflow/typedefs";
export declare const idof: (acct: InteractionAccount) => string;
export declare function sig(opts: Partial<InteractionAccount>): string;
interface IAuthzOpts {
    signingFunction?: (signable: any) => any;
}
export declare function authzFn(opts?: IAuthzOpts): (account: Partial<InteractionAccount>) => Partial<InteractionAccount>;
interface IAuthzResolveOpts {
    tempId?: string;
}
export declare function authzResolve(opts?: IAuthzResolveOpts): (account: InteractionAccount) => {
    tempId: string;
    resolve: (account: Partial<InteractionAccount>) => Partial<InteractionAccount>;
    kind: import("@onflow/typedefs").InteractionResolverKind.ACCOUNT;
    addr: string | null;
    keyId: number | string | null;
    sequenceNum: number | null;
    signature: string | null;
    signingFunction: any | null;
    role: {
        proposer: boolean;
        authorizer: boolean;
        payer: boolean;
        param?: boolean;
    };
    authorization: any;
};
interface IAuthzResolveMany {
    tempId?: string;
    authorizations: any[];
    proposer?: any;
    payer?: any;
}
export declare function authzResolveMany(opts?: IAuthzResolveMany): (account: InteractionAccount) => InteractionAccount;
export declare function authzDeepResolveMany(opts?: IAuthzResolveMany, depth?: number): (account: InteractionAccount) => InteractionAccount;
export {};
