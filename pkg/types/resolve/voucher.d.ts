import { Voucher } from "../encode/encode";
import { Interaction } from "@onflow/typedefs";
export declare function findInsideSigners(ix: Interaction): string[];
export declare function findOutsideSigners(ix: Interaction): string[];
export declare const createSignableVoucher: (ix: Interaction) => {
    cadence: string;
    refBlock: string | null;
    computeLimit: number;
    arguments: any[];
    proposalKey: {
        address: string | null;
        keyId: string | number | null;
        sequenceNum: number | null;
    } | {
        address?: undefined;
        keyId?: undefined;
        sequenceNum?: undefined;
    };
    payer: string | null;
    authorizers: (string | null)[];
    payloadSigs: {
        address: string | null;
        keyId: string | number | null;
        sig: string | null;
    }[];
    envelopeSigs: {
        address: string | null;
        keyId: string | number | null;
        sig: string | null;
    }[];
};
export declare const voucherToTxId: (voucher: Voucher) => string;
