import { Interaction, InteractionAccount } from "@onflow/typedefs";
export declare function resolveSignatures(ix: Interaction): Promise<Interaction>;
export declare function buildSignable(acct: InteractionAccount, message: string, ix: Interaction): {
    f_type: string;
    f_vsn: string;
    message: string;
    addr: string | null;
    keyId: string | number | null;
    roles: {
        proposer: boolean;
        authorizer: boolean;
        payer: boolean;
        param?: boolean;
    };
    cadence: string;
    args: any[];
    data: {};
    interaction: Interaction;
    voucher: {
        cadence: string;
        refBlock: string | null;
        computeLimit: number;
        arguments: any[];
        proposalKey: {
            address: string | null;
            keyId: string | number | null;
            sequenceNum: number | null;
        } | {
            address?: undefined;
            keyId?: undefined;
            sequenceNum?: undefined;
        };
        payer: string | null;
        authorizers: (string | null)[];
        payloadSigs: {
            address: string | null;
            keyId: string | number | null;
            sig: string | null;
        }[];
        envelopeSigs: {
            address: string | null;
            keyId: string | number | null;
            sig: string | null;
        }[];
    };
};
