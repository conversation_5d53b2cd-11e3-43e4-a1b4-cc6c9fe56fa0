import { Interaction, InteractionAccount } from "@onflow/typedefs";
export declare function buildPreSignable(acct: Partial<InteractionAccount>, ix: Interaction): {
    f_type: string;
    f_vsn: string;
    roles: {
        proposer: boolean;
        authorizer: boolean;
        payer: boolean;
        param?: boolean;
    } | undefined;
    cadence: string;
    args: any[];
    data: {};
    interaction: Interaction;
    voucher: {
        cadence: string;
        refBlock: string | null;
        computeLimit: number;
        arguments: any[];
        proposalKey: {
            address: string | null;
            keyId: string | number | null;
            sequenceNum: number | null;
        } | {
            address?: undefined;
            keyId?: undefined;
            sequenceNum?: undefined;
        };
        payer: string | null;
        authorizers: (string | null)[];
        payloadSigs: {
            address: string | null;
            keyId: string | number | null;
            sig: string | null;
        }[];
        envelopeSigs: {
            address: string | null;
            keyId: string | number | null;
            sig: string | null;
        }[];
    };
};
export declare function resolveAccounts(ix: Interaction, opts?: Record<string, any>): Promise<Interaction>;
