import { StreamConnection } from "@onflow/typedefs";
type DecodeResponseFn = (response: Record<string, any>, customDecoders?: Record<string, any>) => Promise<any>;
/**
 * Pipes a generic stream of data into a granular stream of decoded data
 * The data is decoded per channel and emitted in order
 */
export declare const decodeStream: (stream: StreamConnection<{
    data: any;
}>, decodeResponse: DecodeResponseFn, customDecoders?: Record<string, any>) => StreamConnection<any>;
export {};
