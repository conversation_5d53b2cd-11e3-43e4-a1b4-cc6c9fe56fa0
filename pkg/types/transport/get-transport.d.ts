import { SdkTransport } from "@onflow/typedefs";
/**
 * Get the SDK transport object, either from the provided override or from the global config.
 * @param overrides - Override default configuration with custom transport or send function.
 * @returns The SDK transport object.
 */
export declare function getTransport(override?: {
    send?: SdkTransport["send"];
    transport?: SdkTransport;
}): Promise<SdkTransport>;
