<!-- Generated by SpecStory -->

# Convert ts to esm and add packaging logic (2025-06-19 17:01:40)

_**User**_

1.请通过 tsc 对 fcl-js/packages/sdk 进行 ts 转 esm，输出到 fcl-js/pkg 文件夹，并在fcl-js/pkg 文件夹中，加入打包逻辑代码
2.sdk 入口文件：fcl-js/packages/sdk/sdk.ts
3.使用简洁的方式进行 tsc 打包：，能复用项目方配置就复用，不能复用可以重写，例：package.json
script: {
"build:esm": "tsc -p ../tsconfig.json"
}

---

_**Assistant**_

我来帮你使用 TypeScript 编译器将 `fcl-js/packages/sdk` 转换为 ESM 格式，并输出到 `fcl-js/pkg` 文件夹。首先让我了解项目结构和现有配置。

---

<details>
            <summary>Listed current directory • **23** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📄 `package-lock.json` |
| 📁 `packages` |
| 📄 `.gitignore` |
| 📄 `tsconfig.json` |
| 📁 `node_modules` |
| 📁 `.cursor` |
| 📄 `.cursorindexingignore` |
| 📁 `.specstory` |
| 📄 `package.json` |
| 📄 `jest.config.js` |
| 📄 `lerna.json` |
| 📄 `SECURITY.md` |
| 📄 `NOTICE` |
| 📄 `README.md` |
| 📄 `CONTRIBUTING.md` |
| 📄 `LICENSE` |
| 📄 `Makefile` |
| 📄 `.prettierrc` |
| 📄 `CODEOWNERS` |
| 📄 `CODE_OF_CONDUCT.md` |
| 📄 `.prettierignore` |
| 📁 `.changeset` |

</details>

---

<details>
            <summary>Listed directory packages • **6** results</summary>
        
| Name |
|-------|
| 📁 `typedefs` |
| 📁 `fcl-bundle` |
| 📁 `transport-http` |
| 📁 `util-address` |
| 📁 `config` |
| 📁 `sdk` |

</details>

---

<details>
            <summary>Listed directory packages/sdk • **11** results</summary>
        
| Name |
|-------|
| 📄 `package.json` |
| 📁 `node_modules` |
| 📄 `tsconfig.json` |
| 📁 `src` |
| 📄 `TRANSITIONS.md` |
| 📄 `readme.md` |
| 📄 `.browserslistrc` |
| 📄 `.eslintrc.json` |
| 📄 `.npmignore` |
| 📄 `CHANGELOG.md` |
| 📄 `.babelrc` |

</details>

---

Could not resolve URI: Error: 无法解析不存在的文件 '/Users/<USER>/Me/work/3_wallet/project_code_sdk/flow/fcl-js/pkg'

---

pkg 目录不存在，我需要创建它。让我先查看相关配置文件：

---

Read file: tsconfig.json

---

Read file: packages/sdk/tsconfig.json

---

Read file: packages/sdk/package.json

---

<details>
            <summary>Searched codebase "sdk.ts" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

现在让我查看 sdk.ts 入口文件的内容：

---

Read file: packages/sdk/src/sdk.ts

---

现在我创建 pkg 目录并设置编译配置：

---

```bash
mkdir -p /Users/<USER>/Me/work/3_wallet/project_code_sdk/flow/fcl-js/pkg
```

```
 ~/Me/work/3_wallet/project_code_sdk/flow/fcl-js   master ±  mkdir -p /Users/<USER>/Me/work/3_wallet/project_code_s
dk/flow/fcl-js/pkg   mkdir -p /Users/<USER>/Me/work/3_wallet/project_code_sdk/flow/fcl-js/pkg
```

---

现在在 pkg 目录中创建 package.json 和 tsconfig.json：

---

```diff
- 
+ {
+   "name": "fcl-sdk-build",
+   "version": "1.0.0",
+   "private": true,
+   "type": "module",
+   "scripts": {
+     "build:esm": "tsc -p ./tsconfig.json"
+   },
+   "devDependencies": {
+     "typescript": "^4.9.5"
+   }
+ }
```

---

```diff
- 
+ {
+   "extends": "../tsconfig.json",
+   "compilerOptions": {
+     "module": "ESNext",
+     "target": "ES2020",
+     "moduleResolution": "Bundler",
+     "outDir": "./dist",
+     "rootDir": "../packages/sdk/src",
+     "declaration": true,
+     "declarationMap": true,
+     "sourceMap": true,
+     "emitDeclarationOnly": false,
+     "strict": true,
+     "esModuleInterop": true,
+     "allowSyntheticDefaultImports": true,
+     "skipLibCheck": true,
+     "forceConsistentCasingInFileNames": true
+   },
+   "include": [
+     "../packages/sdk/src/**/*"
+   ],
+   "exclude": [
+     "../packages/sdk/src/**/*.test.ts",
+     "../packages/sdk/src/**/*.test.js",
+     "node_modules"
+   ]
+ }
```

---

现在运行 TypeScript 编译：

---

```bash
cd pkg && npm run build:esm
```

```
 ~/Me/work/3_wallet/project_code_sdk/flow/fcl-js   master ±  cd pkg && npm run build:esm

> fcl-sdk-build@1.0.0 build:esm
> tsc -p ./tsconfig.json

../packages/sdk/src/account/account.ts:1:28 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type
 declarations.

1 import type {Account} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/account/account.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its correspondin
g type declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/block/block.ts:1:26 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type dec
larations.

1 import type {Block} from "@onflow/typedefs"
                           ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/block/block.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corresponding ty
pe declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-arguments.ts:2:51 - error TS2307: Cannot find module '@onflow/types' or its corresponding t
ype declarations.

2 import {TypeDescriptorInput, TypeDescriptor} from "@onflow/types"
                                                    ~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-at-block-height.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corres
ponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-at-block-id.ts:9:27 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

9 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-at-latest-block.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corres
ponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-authorizations.ts:1:51 - error TS2307: Cannot find module '@onflow/typedefs' or its corresp
onding type declarations.

1 import {InteractionAccount, TransactionRole} from "@onflow/typedefs"
                                                    ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-get-account.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' or its corres
ponding type declarations.

1 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-get-latest-block.ts:1:19 - error TS2307: Cannot find module '@onflow/util-logger' or its co
rresponding type declarations.

1 import {log} from "@onflow/util-logger"
                    ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-payer.ts:1:31 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding ty
pe declarations.

1 import {TransactionRole} from "@onflow/typedefs"
                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-proposer.ts:1:31 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding
 type declarations.

1 import {TransactionRole} from "@onflow/typedefs"
                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-script.ts:7:24 - error TS2307: Cannot find module '@onflow/util-template' or its correspond
ing type declarations.

7 import {template} from "@onflow/util-template"
                         ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-subscribe-events.ts:1:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its
 corresponding type declarations.

1 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-subscribe-events.ts:8:40 - error TS2307: Cannot find module '@onflow/typedefs' or its corre
sponding type declarations.

8 import {EventFilter, Interaction} from "@onflow/typedefs"
                                         ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-transaction.ts:8:24 - error TS2307: Cannot find module '@onflow/util-template' or its corre
sponding type declarations.

8 import {template} from "@onflow/util-template"
                         ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build.ts:6:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type dec
larations.

6 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/decode/decode-stream.ts:2:32 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding
 type declarations.

2 import {StreamConnection} from "@onflow/typedefs"
                                 ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/decode/decode-stream.ts:24:27 - error TS7006: Parameter 'data' implicitly has an 'any' type.

24   stream.on("data", async data => {
                             ~~~~

../packages/sdk/src/decode/decode.ts:1:19 - error TS2307: Cannot find module '@onflow/util-logger' or its corresponding typ
e declarations.

1 import {log} from "@onflow/util-logger"
                    ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/decode/sdk-decode.ts:1:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding type
 declarations.

1 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/encode/encode.ts:2:43 - error TS2307: Cannot find module '@onflow/rlp' or its corresponding type declar
ations.

2 import {encode, Buffer, EncodeInput} from "@onflow/rlp"
                                            ~~~~~~~~~~~~~

../packages/sdk/src/encode/encode.ts:3:26 - error TS2307: Cannot find module '@onflow/util-address' or its corresponding ty
pe declarations.

3 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:1:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corr
esponding type declarations.

1 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:3:27 - error TS2307: Cannot find module '@onflow/util-logger' or its corresp
onding type declarations.

3 import {log, LEVELS} from "@onflow/util-logger"
                            ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:12:8 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

12 } from "@onflow/typedefs"
          ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:13:51 - error TS2307: Cannot find module '@onflow/types' or its correspondin
g type declarations.

13 import {TypeDescriptorInput, TypeDescriptor} from "@onflow/types"
                                                     ~~~~~~~~~~~~~~~

../packages/sdk/src/node-version-info/node-version-info.ts:1:31 - error TS2307: Cannot find module '@onflow/typedefs' or it
s corresponding type declarations.

1 import {NodeVersionInfo} from "@onflow/typedefs"
                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' or its corre
sponding type declarations.

1 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its cor
responding type declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:3:19 - error TS2307: Cannot find module '@onflow/util-logger' or its corres
ponding type declarations.

3 import {log} from "@onflow/util-logger"
                    ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:5:47 - error TS2307: Cannot find module '@onflow/typedefs' or its correspon
ding type declarations.

5 import {Interaction, InteractionAccount} from "@onflow/typedefs"
                                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:64:38 - error TS7006: Parameter 'd' implicitly has an 'any' type.

64       args: ix.message.arguments.map(d => ix.arguments[d].asArgument),
                                        ~

../packages/sdk/src/resolve/resolve-arguments.ts:1:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its co
rresponding type declarations.

1 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-arguments.ts:3:27 - error TS2307: Cannot find module '@onflow/typedefs' or its correspo
nding type declarations.

3 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corr
esponding type declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:3:22 - error TS2307: Cannot find module '@onflow/config' or its correspondin
g type declarations.

3 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:4:25 - error TS2307: Cannot find module '@onflow/util-logger' or its corresp
onding type declarations.

4 import * as logger from "@onflow/util-logger"
                          ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:5:26 - error TS2307: Cannot find module '@onflow/util-address' or its corres
ponding type declarations.

5 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:6:27 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

6 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:45:13 - error TS7006: Parameter 'd' implicitly has an 'any' type.

45       .then(d =>
               ~

../packages/sdk/src/resolve/resolve-compute-limit.ts:2:22 - error TS2307: Cannot find module '@onflow/config' or its corres
ponding type declarations.

2 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-compute-limit.ts:3:25 - error TS2307: Cannot find module '@onflow/util-logger' or its c
orresponding type declarations.

3 import * as logger from "@onflow/util-logger"
                          ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-compute-limit.ts:4:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corr
esponding type declarations.

4 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-final-normalization.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' o
r its corresponding type declarations.

1 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-final-normalization.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or it
s corresponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:4:22 - error TS2307: Cannot find module '@onflow/config' or
 its corresponding type declarations.

4 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:8:25 - error TS2307: Cannot find module '@onflow/util-invar
iant' or its corresponding type declarations.

8 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:9:22 - error TS2307: Cannot find module '@onflow/rlp' or it
s corresponding type declarations.

9 import {Buffer} from "@onflow/rlp"
                       ~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:10:35 - error TS2307: Cannot find module '@onflow/transport
-http' or its corresponding type declarations.

10 import {send as defaultSend} from "@onflow/transport-http"
                                     ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:11:27 - error TS2307: Cannot find module '@onflow/typedefs'
 or its corresponding type declarations.

11 import {Interaction} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:9:22 - error TS2307: Cannot find module '@onflow/config' or its corresp
onding type declarations.

9 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:12:25 - error TS2307: Cannot find module '@onflow/util-invariant' or it
s corresponding type declarations.

12 import {invariant} from "@onflow/util-invariant"
                           ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:13:22 - error TS2307: Cannot find module '@onflow/rlp' or its correspon
ding type declarations.

13 import {Buffer} from "@onflow/rlp"
                        ~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:14:35 - error TS2307: Cannot find module '@onflow/transport-http' or it
s corresponding type declarations.

14 import {send as defaultSend} from "@onflow/transport-http"
                                     ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:2:47 - error TS2307: Cannot find module '@onflow/typedefs' or its corresp
onding type declarations.

2 import {Interaction, InteractionAccount} from "@onflow/typedefs"
                                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:3:26 - error TS2307: Cannot find module '@onflow/util-address' or its cor
responding type declarations.

3 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:28:43 - error TS2345: Argument of type '(id: string) => Promise<void>' is
 not assignable to parameter of type '(value: unknown, index: number, array: unknown[]) => Promise<void>'.
  Types of parameters 'id' and 'value' are incompatible.
    Type 'unknown' is not assignable to type 'string'.

28       await Promise.all(insideSigners.map(fetchSignature(ix, insidePayload)))
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:46:44 - error TS2345: Argument of type '(id: string) => Promise<void>' is
 not assignable to parameter of type '(value: unknown, index: number, array: unknown[]) => Promise<void>'.
  Types of parameters 'id' and 'value' are incompatible.
    Type 'unknown' is not assignable to type 'string'.

46       await Promise.all(outsideSigners.map(fetchSignature(ix, outsidePayload)))
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:80:38 - error TS7006: Parameter 'd' implicitly has an 'any' type.

80       args: ix.message.arguments.map(d => ix.arguments[d].asArgument),
                                        ~

../packages/sdk/src/resolve/resolve-signatures.ts:109:41 - error TS7006: Parameter 'id' implicitly has an 'any' type.

109     arguments: ix.message.arguments.map(id => ix.arguments[id].asArgument),
                                            ~~

../packages/sdk/src/resolve/resolve-signatures.ts:113:12 - error TS7006: Parameter 'cid' implicitly has an 'any' type.

113       .map(cid => sansPrefix(ix.accounts[cid].addr) || "")
               ~~~

../packages/sdk/src/resolve/resolve-signatures.ts:114:32 - error TS7006: Parameter 'current' implicitly has an 'any' type.

114       .reduce((prev: string[], current) => {
                                   ~~~~~~~

../packages/sdk/src/resolve/resolve-validators.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresp
onding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-voucher-intercept.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its 
corresponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:2:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding type d
eclarations.

2 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:3:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its correspondin
g type declarations.

3 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:4:22 - error TS2307: Cannot find module '@onflow/rlp' or its corresponding type decl
arations.

4 import {Buffer} from "@onflow/rlp"
                       ~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:5:35 - error TS2307: Cannot find module '@onflow/transport-http' or its correspondin
g type declarations.

5 import {send as defaultSend} from "@onflow/transport-http"
                                    ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:12:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding typ
e declarations.

12 import {Interaction} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:109:22 - error TS2339: Property 'sequenceNum' does not exist on type '{}'.

109     if (acct && acct.sequenceNum == null) {
                         ~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:121:24 - error TS2339: Property 'tempId' does not exist on type '{}'.

121       ix.accounts[acct.tempId].sequenceNum = await sendFn(
                           ~~~~~~

../packages/sdk/src/resolve/resolve.ts:122:38 - error TS2339: Property 'addr' does not exist on type '{}'.

122         await build([getAccount(acct.addr!)]),
                                         ~~~~

../packages/sdk/src/resolve/resolve.ts:128:74 - error TS2339: Property 'keyId' does not exist on type '{}'.

128         .then((keys: any) => keys.find((key: any) => key.index === acct!.keyId))
                                                                             ~~~~~

../packages/sdk/src/resolve/voucher.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' or its corresponding 
type declarations.

1 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/voucher.ts:3:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type
 declarations.

3 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/voucher.ts:12:22 - error TS7006: Parameter 'p' implicitly has an 'any' type.

12     ix.payer.forEach(p => inside.delete(p))
                        ~

../packages/sdk/src/resolve/voucher.ts:28:12 - error TS7006: Parameter 'cid' implicitly has an 'any' type.

28       .map(cid => withPrefix(ix.accounts[cid].addr))
              ~~~

../packages/sdk/src/resolve/voucher.ts:29:41 - error TS7006: Parameter 'current' implicitly has an 'any' type.

29       .reduce((prev: (string | null)[], current) => {
                                           ~~~~~~~

../packages/sdk/src/resolve/voucher.ts:61:41 - error TS7006: Parameter 'id' implicitly has an 'any' type.

61     arguments: ix.message.arguments.map(id => ix.arguments[id].asArgument),
                                           ~~

../packages/sdk/src/sdk.ts:1:25 - error TS2307: Cannot find module '@onflow/util-logger' or its corresponding type declarat
ions.

1 import * as logger from "@onflow/util-logger"
                          ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:51:35 - error TS2307: Cannot find module '@onflow/util-template' or its corresponding type decla
rations.

51 export {template as cadence} from "@onflow/util-template"
                                     ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:52:31 - error TS2307: Cannot find module '@onflow/util-template' or its corresponding type decla
rations.

52 export {template as cdc} from "@onflow/util-template"
                                 ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:103:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding type declaration
s.

103 export {config} from "@onflow/config"
                         ~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:130:15 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type declarati
ons.

130 export * from "@onflow/typedefs"
                  ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:132:24 - error TS2307: Cannot find module '@onflow/types' or its corresponding type declarations
.

132 import * as types from "@onflow/types"
                           ~~~~~~~~~~~~~~~

../packages/sdk/src/test-utils/authz-fn.ts:1:34 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding 
type declarations.

1 import {InteractionAccount} from "@onflow/typedefs"
                                   ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/test-utils/authz-fn.ts:2:26 - error TS2307: Cannot find module '@onflow/util-address' or its correspond
ing type declarations.

2 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/test-utils/run.ts:4:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type 
declarations.

4 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:1:22 - error TS2307: Cannot find module '@onflow/config' or its correspondin
g type declarations.

1 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:2:49 - error TS2307: Cannot find module '@onflow/transport-http' or its corr
esponding type declarations.

2 import {httpTransport as defaultTransport} from "@onflow/transport-http"
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:3:28 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

3 import {SdkTransport} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:4:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corr
esponding type declarations.

4 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/send/send.ts:1:22 - error TS2307: Cannot find module '@onflow/rlp' or its corresponding type 
declarations.

1 import {Buffer} from "@onflow/rlp"
                       ~~~~~~~~~~~~~

../packages/sdk/src/transport/send/send.ts:6:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding ty
pe declarations.

6 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.ts:1:22 - error TS2307: Cannot find module '@onflow/config' or its co
rresponding type declarations.

1 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.ts:2:47 - error TS2307: Cannot find module '@onflow/typedefs' or its 
corresponding type declarations.

2 import {SdkTransport, SubscriptionTopic} from "@onflow/typedefs"
                                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.ts:4:25 - error TS2307: Cannot find module '@onflow/util-invariant' o
r its corresponding type declarations.

4 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe.ts:1:61 - error TS2307: Cannot find module '@onflow/typedefs' or its corr
esponding type declarations.

1 import {SdkTransport, Subscription, SubscriptionTopic} from "@onflow/typedefs"
                                                              ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/types.ts:6:8 - error TS2307: Cannot find module '@onflow/typedefs' or its correspon
ding type declarations.

6 } from "@onflow/typedefs"
         ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/wallet-utils/encode-signable.ts:1:38 - error TS2307: Cannot find module '@onflow/util-address' or its c
orresponding type declarations.

1 import {withPrefix, sansPrefix} from "@onflow/util-address"
                                       ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/wallet-utils/validate-tx.ts:5:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its cor
responding type declarations.

5 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~


Found 103 errors in 43 files.

Errors  Files
     2  ../packages/sdk/src/account/account.ts:1
     2  ../packages/sdk/src/block/block.ts:1
     1  ../packages/sdk/src/build/build-arguments.ts:2
     1  ../packages/sdk/src/build/build-at-block-height.ts:2
     1  ../packages/sdk/src/build/build-at-block-id.ts:9
     1  ../packages/sdk/src/build/build-at-latest-block.ts:2
     1  ../packages/sdk/src/build/build-authorizations.ts:1
     1  ../packages/sdk/src/build/build-get-account.ts:1
     1  ../packages/sdk/src/build/build-get-latest-block.ts:1
     1  ../packages/sdk/src/build/build-payer.ts:1
     1  ../packages/sdk/src/build/build-proposer.ts:1
     1  ../packages/sdk/src/build/build-script.ts:7
     2  ../packages/sdk/src/build/build-subscribe-events.ts:1
     1  ../packages/sdk/src/build/build-transaction.ts:8
     1  ../packages/sdk/src/build/build.ts:6
     2  ../packages/sdk/src/decode/decode-stream.ts:2
     1  ../packages/sdk/src/decode/decode.ts:1
     1  ../packages/sdk/src/decode/sdk-decode.ts:1
     2  ../packages/sdk/src/encode/encode.ts:2
     4  ../packages/sdk/src/interaction/interaction.ts:1
     1  ../packages/sdk/src/node-version-info/node-version-info.ts:1
     5  ../packages/sdk/src/resolve/resolve-accounts.ts:1
     2  ../packages/sdk/src/resolve/resolve-arguments.ts:1
     6  ../packages/sdk/src/resolve/resolve-cadence.ts:2
     3  ../packages/sdk/src/resolve/resolve-compute-limit.ts:2
     2  ../packages/sdk/src/resolve/resolve-final-normalization.ts:1
     5  ../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:4
     4  ../packages/sdk/src/resolve/resolve-ref-block-id.ts:9
     8  ../packages/sdk/src/resolve/resolve-signatures.ts:2
     1  ../packages/sdk/src/resolve/resolve-validators.ts:2
     1  ../packages/sdk/src/resolve/resolve-voucher-intercept.ts:2
     9  ../packages/sdk/src/resolve/resolve.ts:2
     6  ../packages/sdk/src/resolve/voucher.ts:1
     6  ../packages/sdk/src/sdk.ts:1
     2  ../packages/sdk/src/test-utils/authz-fn.ts:1
     1  ../packages/sdk/src/test-utils/run.ts:4
     4  ../packages/sdk/src/transport/get-transport.ts:1
     2  ../packages/sdk/src/transport/send/send.ts:1
     3  ../packages/sdk/src/transport/subscribe/subscribe-raw.ts:1
     1  ../packages/sdk/src/transport/subscribe/subscribe.ts:1
     1  ../packages/sdk/src/transport/subscribe/types.ts:6
     1  ../packages/sdk/src/wallet-utils/encode-signable.ts:1
     1  ../packages/sdk/src/wallet-utils/validate-tx.ts:5
 ✘  ~/Me/work/3_wallet/project_code_sdk/flow/fcl-js/pkg   master ± 
```

---

编译时遇到依赖包找不到的问题。我需要安装依赖或者复制必要的类型定义。让我检查 packages 目录下其他包的结构：

---

```bash
cd .. && npm install
```

```
 ✘  ~/Me/work/3_wallet/project_code_sdk/flow/fcl-js/pkg   master ±  cd .. && npm install

added 1 package, removed 1308 packages, and changed 12 packages in 25s

144 packages are looking for funding
  run `npm fund` for details
 ~/Me/work/3_wallet/project_code_sdk/flow/fcl-js   master ± 
```

---

现在让我修改 tsconfig.json 的配置，添加 node_modules 路径解析：

---

```diff
      "module": "ESNext",
      "target": "ES2020",
-     "moduleResolution": "Bundler",
+     "moduleResolution": "Node",
      "outDir": "./dist",
      "rootDir": "../packages/sdk/src",
```

```diff
      "allowSyntheticDefaultImports": true,
      "skipLibCheck": true,
-     "forceConsistentCasingInFileNames": true
+     "forceConsistentCasingInFileNames": true,
+     "baseUrl": "..",
+     "paths": {
+       "@onflow/*": ["packages/*/src", "node_modules/@onflow/*"]
+     }
    },
    "include": [
```

---

现在重新运行编译：

---

```bash
cd pkg && npm run build:esm
```

```

> fcl-sdk-build@1.0.0 build:esm
> tsc -p ./tsconfig.json

../packages/sdk/src/account/account.ts:1:28 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type
 declarations.

1 import type {Account} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/account/account.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its correspondin
g type declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/block/block.ts:1:26 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type dec
larations.

1 import type {Block} from "@onflow/typedefs"
                           ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/block/block.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corresponding ty
pe declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-arguments.ts:2:51 - error TS2307: Cannot find module '@onflow/types' or its corresponding t
ype declarations.

2 import {TypeDescriptorInput, TypeDescriptor} from "@onflow/types"
                                                    ~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-at-block-height.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corres
ponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-at-block-id.ts:9:27 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

9 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-at-latest-block.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corres
ponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-authorizations.ts:1:51 - error TS2307: Cannot find module '@onflow/typedefs' or its corresp
onding type declarations.

1 import {InteractionAccount, TransactionRole} from "@onflow/typedefs"
                                                    ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-get-account.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' or its corres
ponding type declarations.

1 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-get-latest-block.ts:1:19 - error TS2307: Cannot find module '@onflow/util-logger' or its co
rresponding type declarations.

1 import {log} from "@onflow/util-logger"
                    ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-payer.ts:1:31 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding ty
pe declarations.

1 import {TransactionRole} from "@onflow/typedefs"
                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-proposer.ts:1:31 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding
 type declarations.

1 import {TransactionRole} from "@onflow/typedefs"
                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-script.ts:7:24 - error TS2307: Cannot find module '@onflow/util-template' or its correspond
ing type declarations.

7 import {template} from "@onflow/util-template"
                         ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-subscribe-events.ts:1:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its
 corresponding type declarations.

1 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-subscribe-events.ts:8:40 - error TS2307: Cannot find module '@onflow/typedefs' or its corre
sponding type declarations.

8 import {EventFilter, Interaction} from "@onflow/typedefs"
                                         ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-transaction.ts:8:24 - error TS2307: Cannot find module '@onflow/util-template' or its corre
sponding type declarations.

8 import {template} from "@onflow/util-template"
                         ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build.ts:6:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type dec
larations.

6 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/decode/decode-stream.ts:2:32 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding
 type declarations.

2 import {StreamConnection} from "@onflow/typedefs"
                                 ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/decode/decode-stream.ts:24:27 - error TS7006: Parameter 'data' implicitly has an 'any' type.

24   stream.on("data", async data => {
                             ~~~~

../packages/sdk/src/decode/decode.ts:1:19 - error TS2307: Cannot find module '@onflow/util-logger' or its corresponding typ
e declarations.

1 import {log} from "@onflow/util-logger"
                    ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/decode/sdk-decode.ts:1:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding type
 declarations.

1 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/encode/encode.ts:2:43 - error TS2307: Cannot find module '@onflow/rlp' or its corresponding type declar
ations.

2 import {encode, Buffer, EncodeInput} from "@onflow/rlp"
                                            ~~~~~~~~~~~~~

../packages/sdk/src/encode/encode.ts:3:26 - error TS2307: Cannot find module '@onflow/util-address' or its corresponding ty
pe declarations.

3 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:1:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corr
esponding type declarations.

1 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:3:27 - error TS2307: Cannot find module '@onflow/util-logger' or its corresp
onding type declarations.

3 import {log, LEVELS} from "@onflow/util-logger"
                            ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:12:8 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

12 } from "@onflow/typedefs"
          ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:13:51 - error TS2307: Cannot find module '@onflow/types' or its correspondin
g type declarations.

13 import {TypeDescriptorInput, TypeDescriptor} from "@onflow/types"
                                                     ~~~~~~~~~~~~~~~

../packages/sdk/src/node-version-info/node-version-info.ts:1:31 - error TS2307: Cannot find module '@onflow/typedefs' or it
s corresponding type declarations.

1 import {NodeVersionInfo} from "@onflow/typedefs"
                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' or its corre
sponding type declarations.

1 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its cor
responding type declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:3:19 - error TS2307: Cannot find module '@onflow/util-logger' or its corres
ponding type declarations.

3 import {log} from "@onflow/util-logger"
                    ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:5:47 - error TS2307: Cannot find module '@onflow/typedefs' or its correspon
ding type declarations.

5 import {Interaction, InteractionAccount} from "@onflow/typedefs"
                                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:64:38 - error TS7006: Parameter 'd' implicitly has an 'any' type.

64       args: ix.message.arguments.map(d => ix.arguments[d].asArgument),
                                        ~

../packages/sdk/src/resolve/resolve-arguments.ts:1:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its co
rresponding type declarations.

1 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-arguments.ts:3:27 - error TS2307: Cannot find module '@onflow/typedefs' or its correspo
nding type declarations.

3 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corr
esponding type declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:3:22 - error TS2307: Cannot find module '@onflow/config' or its correspondin
g type declarations.

3 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:4:25 - error TS2307: Cannot find module '@onflow/util-logger' or its corresp
onding type declarations.

4 import * as logger from "@onflow/util-logger"
                          ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:5:26 - error TS2307: Cannot find module '@onflow/util-address' or its corres
ponding type declarations.

5 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:6:27 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

6 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:45:13 - error TS7006: Parameter 'd' implicitly has an 'any' type.

45       .then(d =>
               ~

../packages/sdk/src/resolve/resolve-compute-limit.ts:2:22 - error TS2307: Cannot find module '@onflow/config' or its corres
ponding type declarations.

2 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-compute-limit.ts:3:25 - error TS2307: Cannot find module '@onflow/util-logger' or its c
orresponding type declarations.

3 import * as logger from "@onflow/util-logger"
                          ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-compute-limit.ts:4:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corr
esponding type declarations.

4 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-final-normalization.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' o
r its corresponding type declarations.

1 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-final-normalization.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or it
s corresponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:4:22 - error TS2307: Cannot find module '@onflow/config' or
 its corresponding type declarations.

4 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:8:25 - error TS2307: Cannot find module '@onflow/util-invar
iant' or its corresponding type declarations.

8 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:9:22 - error TS2307: Cannot find module '@onflow/rlp' or it
s corresponding type declarations.

9 import {Buffer} from "@onflow/rlp"
                       ~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:10:35 - error TS2307: Cannot find module '@onflow/transport
-http' or its corresponding type declarations.

10 import {send as defaultSend} from "@onflow/transport-http"
                                     ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:11:27 - error TS2307: Cannot find module '@onflow/typedefs'
 or its corresponding type declarations.

11 import {Interaction} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:9:22 - error TS2307: Cannot find module '@onflow/config' or its corresp
onding type declarations.

9 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:12:25 - error TS2307: Cannot find module '@onflow/util-invariant' or it
s corresponding type declarations.

12 import {invariant} from "@onflow/util-invariant"
                           ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:13:22 - error TS2307: Cannot find module '@onflow/rlp' or its correspon
ding type declarations.

13 import {Buffer} from "@onflow/rlp"
                        ~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:14:35 - error TS2307: Cannot find module '@onflow/transport-http' or it
s corresponding type declarations.

14 import {send as defaultSend} from "@onflow/transport-http"
                                     ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:2:47 - error TS2307: Cannot find module '@onflow/typedefs' or its corresp
onding type declarations.

2 import {Interaction, InteractionAccount} from "@onflow/typedefs"
                                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:3:26 - error TS2307: Cannot find module '@onflow/util-address' or its cor
responding type declarations.

3 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:28:43 - error TS2345: Argument of type '(id: string) => Promise<void>' is
 not assignable to parameter of type '(value: unknown, index: number, array: unknown[]) => Promise<void>'.
  Types of parameters 'id' and 'value' are incompatible.
    Type 'unknown' is not assignable to type 'string'.

28       await Promise.all(insideSigners.map(fetchSignature(ix, insidePayload)))
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:46:44 - error TS2345: Argument of type '(id: string) => Promise<void>' is
 not assignable to parameter of type '(value: unknown, index: number, array: unknown[]) => Promise<void>'.
  Types of parameters 'id' and 'value' are incompatible.
    Type 'unknown' is not assignable to type 'string'.

46       await Promise.all(outsideSigners.map(fetchSignature(ix, outsidePayload)))
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:80:38 - error TS7006: Parameter 'd' implicitly has an 'any' type.

80       args: ix.message.arguments.map(d => ix.arguments[d].asArgument),
                                        ~

../packages/sdk/src/resolve/resolve-signatures.ts:109:41 - error TS7006: Parameter 'id' implicitly has an 'any' type.

109     arguments: ix.message.arguments.map(id => ix.arguments[id].asArgument),
                                            ~~

../packages/sdk/src/resolve/resolve-signatures.ts:113:12 - error TS7006: Parameter 'cid' implicitly has an 'any' type.

113       .map(cid => sansPrefix(ix.accounts[cid].addr) || "")
               ~~~

../packages/sdk/src/resolve/resolve-signatures.ts:114:32 - error TS7006: Parameter 'current' implicitly has an 'any' type.

114       .reduce((prev: string[], current) => {
                                   ~~~~~~~

../packages/sdk/src/resolve/resolve-validators.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresp
onding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-voucher-intercept.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its 
corresponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:2:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding type d
eclarations.

2 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:3:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its correspondin
g type declarations.

3 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:4:22 - error TS2307: Cannot find module '@onflow/rlp' or its corresponding type decl
arations.

4 import {Buffer} from "@onflow/rlp"
                       ~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:5:35 - error TS2307: Cannot find module '@onflow/transport-http' or its correspondin
g type declarations.

5 import {send as defaultSend} from "@onflow/transport-http"
                                    ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:12:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding typ
e declarations.

12 import {Interaction} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:109:22 - error TS2339: Property 'sequenceNum' does not exist on type '{}'.

109     if (acct && acct.sequenceNum == null) {
                         ~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:121:24 - error TS2339: Property 'tempId' does not exist on type '{}'.

121       ix.accounts[acct.tempId].sequenceNum = await sendFn(
                           ~~~~~~

../packages/sdk/src/resolve/resolve.ts:122:38 - error TS2339: Property 'addr' does not exist on type '{}'.

122         await build([getAccount(acct.addr!)]),
                                         ~~~~

../packages/sdk/src/resolve/resolve.ts:128:74 - error TS2339: Property 'keyId' does not exist on type '{}'.

128         .then((keys: any) => keys.find((key: any) => key.index === acct!.keyId))
                                                                             ~~~~~

../packages/sdk/src/resolve/voucher.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' or its corresponding 
type declarations.

1 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/voucher.ts:3:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type
 declarations.

3 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/voucher.ts:12:22 - error TS7006: Parameter 'p' implicitly has an 'any' type.

12     ix.payer.forEach(p => inside.delete(p))
                        ~

../packages/sdk/src/resolve/voucher.ts:28:12 - error TS7006: Parameter 'cid' implicitly has an 'any' type.

28       .map(cid => withPrefix(ix.accounts[cid].addr))
              ~~~

../packages/sdk/src/resolve/voucher.ts:29:41 - error TS7006: Parameter 'current' implicitly has an 'any' type.

29       .reduce((prev: (string | null)[], current) => {
                                           ~~~~~~~

../packages/sdk/src/resolve/voucher.ts:61:41 - error TS7006: Parameter 'id' implicitly has an 'any' type.

61     arguments: ix.message.arguments.map(id => ix.arguments[id].asArgument),
                                           ~~

../packages/sdk/src/sdk.ts:1:25 - error TS2307: Cannot find module '@onflow/util-logger' or its corresponding type declarat
ions.

1 import * as logger from "@onflow/util-logger"
                          ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:51:35 - error TS2307: Cannot find module '@onflow/util-template' or its corresponding type decla
rations.

51 export {template as cadence} from "@onflow/util-template"
                                     ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:52:31 - error TS2307: Cannot find module '@onflow/util-template' or its corresponding type decla
rations.

52 export {template as cdc} from "@onflow/util-template"
                                 ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:103:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding type declaration
s.

103 export {config} from "@onflow/config"
                         ~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:130:15 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type declarati
ons.

130 export * from "@onflow/typedefs"
                  ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:132:24 - error TS2307: Cannot find module '@onflow/types' or its corresponding type declarations
.

132 import * as types from "@onflow/types"
                           ~~~~~~~~~~~~~~~

../packages/sdk/src/test-utils/authz-fn.ts:1:34 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding 
type declarations.

1 import {InteractionAccount} from "@onflow/typedefs"
                                   ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/test-utils/authz-fn.ts:2:26 - error TS2307: Cannot find module '@onflow/util-address' or its correspond
ing type declarations.

2 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/test-utils/run.ts:4:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type 
declarations.

4 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:1:22 - error TS2307: Cannot find module '@onflow/config' or its correspondin
g type declarations.

1 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:2:49 - error TS2307: Cannot find module '@onflow/transport-http' or its corr
esponding type declarations.

2 import {httpTransport as defaultTransport} from "@onflow/transport-http"
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:3:28 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

3 import {SdkTransport} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:4:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corr
esponding type declarations.

4 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/send/send.ts:1:22 - error TS2307: Cannot find module '@onflow/rlp' or its corresponding type 
declarations.

1 import {Buffer} from "@onflow/rlp"
                       ~~~~~~~~~~~~~

../packages/sdk/src/transport/send/send.ts:6:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding ty
pe declarations.

6 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.ts:1:22 - error TS2307: Cannot find module '@onflow/config' or its co
rresponding type declarations.

1 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.ts:2:47 - error TS2307: Cannot find module '@onflow/typedefs' or its 
corresponding type declarations.

2 import {SdkTransport, SubscriptionTopic} from "@onflow/typedefs"
                                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.ts:4:25 - error TS2307: Cannot find module '@onflow/util-invariant' o
r its corresponding type declarations.

4 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe.ts:1:61 - error TS2307: Cannot find module '@onflow/typedefs' or its corr
esponding type declarations.

1 import {SdkTransport, Subscription, SubscriptionTopic} from "@onflow/typedefs"
                                                              ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/types.ts:6:8 - error TS2307: Cannot find module '@onflow/typedefs' or its correspon
ding type declarations.

6 } from "@onflow/typedefs"
         ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/wallet-utils/encode-signable.ts:1:38 - error TS2307: Cannot find module '@onflow/util-address' or its c
orresponding type declarations.

1 import {withPrefix, sansPrefix} from "@onflow/util-address"
                                       ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/wallet-utils/validate-tx.ts:5:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its cor
responding type declarations.

5 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~


Found 103 errors in 43 files.

Errors  Files
     2  ../packages/sdk/src/account/account.ts:1
     2  ../packages/sdk/src/block/block.ts:1
     1  ../packages/sdk/src/build/build-arguments.ts:2
     1  ../packages/sdk/src/build/build-at-block-height.ts:2
     1  ../packages/sdk/src/build/build-at-block-id.ts:9
     1  ../packages/sdk/src/build/build-at-latest-block.ts:2
     1  ../packages/sdk/src/build/build-authorizations.ts:1
     1  ../packages/sdk/src/build/build-get-account.ts:1
     1  ../packages/sdk/src/build/build-get-latest-block.ts:1
     1  ../packages/sdk/src/build/build-payer.ts:1
     1  ../packages/sdk/src/build/build-proposer.ts:1
     1  ../packages/sdk/src/build/build-script.ts:7
     2  ../packages/sdk/src/build/build-subscribe-events.ts:1
     1  ../packages/sdk/src/build/build-transaction.ts:8
     1  ../packages/sdk/src/build/build.ts:6
     2  ../packages/sdk/src/decode/decode-stream.ts:2
     1  ../packages/sdk/src/decode/decode.ts:1
     1  ../packages/sdk/src/decode/sdk-decode.ts:1
     2  ../packages/sdk/src/encode/encode.ts:2
     4  ../packages/sdk/src/interaction/interaction.ts:1
     1  ../packages/sdk/src/node-version-info/node-version-info.ts:1
     5  ../packages/sdk/src/resolve/resolve-accounts.ts:1
     2  ../packages/sdk/src/resolve/resolve-arguments.ts:1
     6  ../packages/sdk/src/resolve/resolve-cadence.ts:2
     3  ../packages/sdk/src/resolve/resolve-compute-limit.ts:2
     2  ../packages/sdk/src/resolve/resolve-final-normalization.ts:1
     5  ../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:4
     4  ../packages/sdk/src/resolve/resolve-ref-block-id.ts:9
     8  ../packages/sdk/src/resolve/resolve-signatures.ts:2
     1  ../packages/sdk/src/resolve/resolve-validators.ts:2
     1  ../packages/sdk/src/resolve/resolve-voucher-intercept.ts:2
     9  ../packages/sdk/src/resolve/resolve.ts:2
     6  ../packages/sdk/src/resolve/voucher.ts:1
     6  ../packages/sdk/src/sdk.ts:1
     2  ../packages/sdk/src/test-utils/authz-fn.ts:1
     1  ../packages/sdk/src/test-utils/run.ts:4
     4  ../packages/sdk/src/transport/get-transport.ts:1
     2  ../packages/sdk/src/transport/send/send.ts:1
     3  ../packages/sdk/src/transport/subscribe/subscribe-raw.ts:1
     1  ../packages/sdk/src/transport/subscribe/subscribe.ts:1
     1  ../packages/sdk/src/transport/subscribe/types.ts:6
     1  ../packages/sdk/src/wallet-utils/encode-signable.ts:1
     1  ../packages/sdk/src/wallet-utils/validate-tx.ts:5

```

---

问题在于 @onflow 依赖包还没有安装。让我检查这些包是否在项目的其他位置，或者需要单独构建：

---

<details>
            <summary>Listed directory packages • **6** results</summary>
        
| Name |
|-------|
| 📁 `typedefs` |
| 📁 `fcl-bundle` |
| 📁 `transport-http` |
| 📁 `util-address` |
| 📁 `config` |
| 📁 `sdk` |

</details>

---

```bash
cd .. && npx lerna bootstrap
```

```
ERR! bootstrap The "bootstrap" command was removed by default in v7, and is no longer maintained.
ERR! bootstrap Learn more about this change at https://lerna.js.org/docs/legacy-package-management

```

---

让我先安装项目的依赖包，然后修改编译配置：

---

```bash
npm install
```

```

changed 12 packages in 7s

144 packages are looking for funding
  run `npm fund` for details

```

---

```bash
cd packages/sdk && npm install
```

```
npm error Cannot set properties of null (setting 'dev')

npm error A complete log of this run can be found in: /Users/<USER>/.npm/_logs/2025-06-19T09_59_47_629Z-debug-0.log

```

---

让我创建一个更简单的配置方案。我将直接复制依赖包的构建输出到 pkg 目录，并创建一个只编译核心 TypeScript 代码的配置：

---

```diff
  {
-   "extends": "../tsconfig.json",
    "compilerOptions": {
+     "target": "ES2020",
      "module": "ESNext",
-     "target": "ES2020",
      "moduleResolution": "Node",
      "outDir": "./dist",
      "rootDir": "../packages/sdk/src",
      "declaration": true,
-     "declarationMap": true,
-     "sourceMap": true,
      "emitDeclarationOnly": false,
-     "strict": true,
      "esModuleInterop": true,
      "allowSyntheticDefaultImports": true,
      "skipLibCheck": true,
-     "forceConsistentCasingInFileNames": true,
-     "baseUrl": "..",
-     "paths": {
-       "@onflow/*": ["packages/*/src", "node_modules/@onflow/*"]
-     }
+     "noImplicitAny": false,
+     "strict": false,
+     "allowJs": true
    },
    "include": [
-     "../packages/sdk/src/**/*"
+     "../packages/sdk/src/sdk.ts"
    ],
    "exclude": [
-     "../packages/sdk/src/**/*.test.ts",
-     "../packages/sdk/src/**/*.test.js",
-     "node_modules"
+     "node_modules",
+     "**/*.test.*"
    ]
  }
```

---

现在让我创建一个简单的依赖 stub 文件来避免模块找不到的错误：

---

```bash
cd pkg && mkdir -p node_modules/@onflow
```

```
cd: no such file or directory: pkg

```

---

```bash
cd ../../pkg && mkdir -p node_modules/@onflow
```

---

现在我使用一个更简单的方法，通过复制 packages 目录下的依赖模块到 pkg 目录：

---

```bash
cp -r ../packages/* node_modules/@onflow/
```

---

现在让我修改 tsconfig.json 配置，使其能正确解析依赖：

---

```diff
      "noImplicitAny": false,
      "strict": false,
-     "allowJs": true
+     "allowJs": true,
+     "baseUrl": ".",
+     "paths": {
+       "@onflow/*": ["node_modules/@onflow/*/src"]
+     }
    },
    "include": [
-     "../packages/sdk/src/sdk.ts"
+     "../packages/sdk/src/**/*"
    ],
    "exclude": [
```

---

现在再次尝试编译：

---

```bash
npm run build:esm
```

```

> fcl-sdk-build@1.0.0 build:esm
> tsc -p ./tsconfig.json

../packages/sdk/src/account/account.ts:1:28 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type
 declarations.

1 import type {Account} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/account/account.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its correspondin
g type declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/block/block.ts:1:26 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type dec
larations.

1 import type {Block} from "@onflow/typedefs"
                           ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/block/block.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corresponding ty
pe declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-arguments.ts:2:51 - error TS2307: Cannot find module '@onflow/types' or its corresponding t
ype declarations.

2 import {TypeDescriptorInput, TypeDescriptor} from "@onflow/types"
                                                    ~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-at-block-height.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corres
ponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-at-block-id.ts:9:27 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

9 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-at-latest-block.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corres
ponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-authorizations.ts:1:51 - error TS2307: Cannot find module '@onflow/typedefs' or its corresp
onding type declarations.

1 import {InteractionAccount, TransactionRole} from "@onflow/typedefs"
                                                    ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-get-account.test.ts:2:38 - error TS2307: Cannot find module '@onflow/util-address' or its c
orresponding type declarations.

2 import {sansPrefix, withPrefix} from "@onflow/util-address"
                                       ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-get-account.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' or its corres
ponding type declarations.

1 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-get-latest-block.ts:1:19 - error TS2307: Cannot find module '@onflow/util-logger' or its co
rresponding type declarations.

1 import {log} from "@onflow/util-logger"
                    ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-payer.test.ts:8:33 - error TS2345: Argument of type '{ addr: string; }' is not assignable t
o parameter of type 'any[]'.
  Type '{ addr: string; }' is missing the following properties from type 'any[]': length, pop, push, concat, and 29 more.

8     let ix = await (await payer(authz))(initInteraction())
                                  ~~~~~

../packages/sdk/src/build/build-payer.ts:1:31 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding ty
pe declarations.

1 import {TransactionRole} from "@onflow/typedefs"
                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-proposer.ts:1:31 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding
 type declarations.

1 import {TransactionRole} from "@onflow/typedefs"
                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-script.ts:7:24 - error TS2307: Cannot find module '@onflow/util-template' or its correspond
ing type declarations.

7 import {template} from "@onflow/util-template"
                         ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-subscribe-events.ts:1:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its
 corresponding type declarations.

1 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-subscribe-events.ts:8:40 - error TS2307: Cannot find module '@onflow/typedefs' or its corre
sponding type declarations.

8 import {EventFilter, Interaction} from "@onflow/typedefs"
                                         ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build-transaction.ts:8:24 - error TS2307: Cannot find module '@onflow/util-template' or its corre
sponding type declarations.

8 import {template} from "@onflow/util-template"
                         ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/build/build.ts:6:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type dec
larations.

6 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/contract.test.ts:6:27 - error TS2307: Cannot find module '@onflow/util-template' or its corresponding t
ype declarations.

6 import * as template from "@onflow/util-template"
                            ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/decode/decode-stream.test.ts:3:32 - error TS2307: Cannot find module '@onflow/typedefs' or its correspo
nding type declarations.

3 import {StreamConnection} from "@onflow/typedefs"
                                 ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/decode/decode-stream.ts:2:32 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding
 type declarations.

2 import {StreamConnection} from "@onflow/typedefs"
                                 ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/decode/decode.ts:1:19 - error TS2307: Cannot find module '@onflow/util-logger' or its corresponding typ
e declarations.

1 import {log} from "@onflow/util-logger"
                    ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/decode/sdk-decode.ts:1:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding type
 declarations.

1 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/encode/encode.ts:2:43 - error TS2307: Cannot find module '@onflow/rlp' or its corresponding type declar
ations.

2 import {encode, Buffer, EncodeInput} from "@onflow/rlp"
                                            ~~~~~~~~~~~~~

../packages/sdk/src/encode/encode.ts:3:26 - error TS2307: Cannot find module '@onflow/util-address' or its corresponding ty
pe declarations.

3 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.test.ts:1:51 - error TS2307: Cannot find module '@onflow/typedefs' or its corre
sponding type declarations.

1 import {InteractionAccount, TransactionRole} from "@onflow/typedefs"
                                                    ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:1:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corr
esponding type declarations.

1 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:3:27 - error TS2307: Cannot find module '@onflow/util-logger' or its corresp
onding type declarations.

3 import {log, LEVELS} from "@onflow/util-logger"
                            ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:12:8 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

12 } from "@onflow/typedefs"
          ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/interaction/interaction.ts:13:51 - error TS2307: Cannot find module '@onflow/types' or its correspondin
g type declarations.

13 import {TypeDescriptorInput, TypeDescriptor} from "@onflow/types"
                                                     ~~~~~~~~~~~~~~~

../packages/sdk/src/node-version-info/node-version-info.ts:1:31 - error TS2307: Cannot find module '@onflow/typedefs' or it
s corresponding type declarations.

1 import {NodeVersionInfo} from "@onflow/typedefs"
                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' or its corre
sponding type declarations.

1 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its cor
responding type declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:3:19 - error TS2307: Cannot find module '@onflow/util-logger' or its corres
ponding type declarations.

3 import {log} from "@onflow/util-logger"
                    ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-accounts.ts:5:47 - error TS2307: Cannot find module '@onflow/typedefs' or its correspon
ding type declarations.

5 import {Interaction, InteractionAccount} from "@onflow/typedefs"
                                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-arguments.ts:1:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its co
rresponding type declarations.

1 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-arguments.ts:3:27 - error TS2307: Cannot find module '@onflow/typedefs' or its correspo
nding type declarations.

3 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.test.ts:8:22 - error TS2307: Cannot find module '@onflow/config' or its corresp
onding type declarations.

8 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:2:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corr
esponding type declarations.

2 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:3:22 - error TS2307: Cannot find module '@onflow/config' or its correspondin
g type declarations.

3 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:4:25 - error TS2307: Cannot find module '@onflow/util-logger' or its corresp
onding type declarations.

4 import * as logger from "@onflow/util-logger"
                          ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:5:26 - error TS2307: Cannot find module '@onflow/util-address' or its corres
ponding type declarations.

5 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-cadence.ts:6:27 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

6 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-compute-limit.test.ts:6:22 - error TS2307: Cannot find module '@onflow/config' or its c
orresponding type declarations.

6 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-compute-limit.ts:2:22 - error TS2307: Cannot find module '@onflow/config' or its corres
ponding type declarations.

2 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-compute-limit.ts:3:25 - error TS2307: Cannot find module '@onflow/util-logger' or its c
orresponding type declarations.

3 import * as logger from "@onflow/util-logger"
                          ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-compute-limit.ts:4:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corr
esponding type declarations.

4 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-final-normalization.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' o
r its corresponding type declarations.

1 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-final-normalization.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or it
s corresponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:4:22 - error TS2307: Cannot find module '@onflow/config' or
 its corresponding type declarations.

4 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:8:25 - error TS2307: Cannot find module '@onflow/util-invar
iant' or its corresponding type declarations.

8 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:9:22 - error TS2307: Cannot find module '@onflow/rlp' or it
s corresponding type declarations.

9 import {Buffer} from "@onflow/rlp"
                       ~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:10:35 - error TS2307: Cannot find module '@onflow/transport
-http' or its corresponding type declarations.

10 import {send as defaultSend} from "@onflow/transport-http"
                                     ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:11:27 - error TS2307: Cannot find module '@onflow/typedefs'
 or its corresponding type declarations.

11 import {Interaction} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:9:22 - error TS2307: Cannot find module '@onflow/config' or its corresp
onding type declarations.

9 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:12:25 - error TS2307: Cannot find module '@onflow/util-invariant' or it
s corresponding type declarations.

12 import {invariant} from "@onflow/util-invariant"
                           ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:13:22 - error TS2307: Cannot find module '@onflow/rlp' or its correspon
ding type declarations.

13 import {Buffer} from "@onflow/rlp"
                        ~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-ref-block-id.ts:14:35 - error TS2307: Cannot find module '@onflow/transport-http' or it
s corresponding type declarations.

14 import {send as defaultSend} from "@onflow/transport-http"
                                     ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.test.ts:12:55 - error TS2307: Cannot find module '@onflow/typedefs' or its c
orresponding type declarations.

12 import {InteractionResolverKind, InteractionTag} from "@onflow/typedefs"
                                                         ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.test.ts:69:9 - error TS2353: Object literal may only specify known propertie
s, and 'addr' does not exist in type 'any[]'.

69         addr: "0x02",
           ~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:2:47 - error TS2307: Cannot find module '@onflow/typedefs' or its corresp
onding type declarations.

2 import {Interaction, InteractionAccount} from "@onflow/typedefs"
                                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-signatures.ts:3:26 - error TS2307: Cannot find module '@onflow/util-address' or its cor
responding type declarations.

3 import {sansPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-validators.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresp
onding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-voucher-intercept.test.ts:1:55 - error TS2307: Cannot find module '@onflow/typedefs' or
 its corresponding type declarations.

1 import {InteractionResolverKind, InteractionTag} from "@onflow/typedefs"
                                                        ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve-voucher-intercept.ts:2:27 - error TS2307: Cannot find module '@onflow/typedefs' or its 
corresponding type declarations.

2 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:2:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding type d
eclarations.

2 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:3:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its correspondin
g type declarations.

3 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:4:22 - error TS2307: Cannot find module '@onflow/rlp' or its corresponding type decl
arations.

4 import {Buffer} from "@onflow/rlp"
                       ~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:5:35 - error TS2307: Cannot find module '@onflow/transport-http' or its correspondin
g type declarations.

5 import {send as defaultSend} from "@onflow/transport-http"
                                    ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:12:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding typ
e declarations.

12 import {Interaction} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:109:22 - error TS2339: Property 'sequenceNum' does not exist on type 'unknown'.

109     if (acct && acct.sequenceNum == null) {
                         ~~~~~~~~~~~

../packages/sdk/src/resolve/resolve.ts:121:24 - error TS2339: Property 'tempId' does not exist on type 'unknown'.

121       ix.accounts[acct.tempId].sequenceNum = await sendFn(
                           ~~~~~~

../packages/sdk/src/resolve/resolve.ts:122:38 - error TS2339: Property 'addr' does not exist on type 'unknown'.

122         await build([getAccount(acct.addr!)]),
                                         ~~~~

../packages/sdk/src/resolve/resolve.ts:128:74 - error TS2339: Property 'keyId' does not exist on type 'unknown'.

128         .then((keys: any) => keys.find((key: any) => key.index === acct!.keyId))
                                                                             ~~~~~

../packages/sdk/src/resolve/voucher.ts:1:26 - error TS2307: Cannot find module '@onflow/util-address' or its corresponding 
type declarations.

1 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/resolve/voucher.ts:3:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type
 declarations.

3 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:1:25 - error TS2307: Cannot find module '@onflow/util-logger' or its corresponding type declarat
ions.

1 import * as logger from "@onflow/util-logger"
                          ~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:51:35 - error TS2307: Cannot find module '@onflow/util-template' or its corresponding type decla
rations.

51 export {template as cadence} from "@onflow/util-template"
                                     ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:52:31 - error TS2307: Cannot find module '@onflow/util-template' or its corresponding type decla
rations.

52 export {template as cdc} from "@onflow/util-template"
                                 ~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:103:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding type declaration
s.

103 export {config} from "@onflow/config"
                         ~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:130:15 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type declarati
ons.

130 export * from "@onflow/typedefs"
                  ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/sdk.ts:132:24 - error TS2307: Cannot find module '@onflow/types' or its corresponding type declarations
.

132 import * as types from "@onflow/types"
                           ~~~~~~~~~~~~~~~

../packages/sdk/src/test-utils/authz-fn.ts:1:34 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding 
type declarations.

1 import {InteractionAccount} from "@onflow/typedefs"
                                   ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/test-utils/authz-fn.ts:2:26 - error TS2307: Cannot find module '@onflow/util-address' or its correspond
ing type declarations.

2 import {withPrefix} from "@onflow/util-address"
                           ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/test-utils/run.ts:4:27 - error TS2307: Cannot find module '@onflow/typedefs' or its corresponding type 
declarations.

4 import {Interaction} from "@onflow/typedefs"
                            ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.test.ts:1:28 - error TS2307: Cannot find module '@onflow/typedefs' or its corre
sponding type declarations.

1 import {SdkTransport} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.test.ts:3:29 - error TS2307: Cannot find module '@onflow/transport-http' or its
 corresponding type declarations.

3 import {httpTransport} from "@onflow/transport-http"
                              ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.test.ts:4:22 - error TS2307: Cannot find module '@onflow/config' or its corresp
onding type declarations.

4 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:1:22 - error TS2307: Cannot find module '@onflow/config' or its correspondin
g type declarations.

1 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:2:49 - error TS2307: Cannot find module '@onflow/transport-http' or its corr
esponding type declarations.

2 import {httpTransport as defaultTransport} from "@onflow/transport-http"
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:3:28 - error TS2307: Cannot find module '@onflow/typedefs' or its correspond
ing type declarations.

3 import {SdkTransport} from "@onflow/typedefs"
                             ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/get-transport.ts:4:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its corr
esponding type declarations.

4 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/send/send.ts:1:22 - error TS2307: Cannot find module '@onflow/rlp' or its corresponding type 
declarations.

1 import {Buffer} from "@onflow/rlp"
                       ~~~~~~~~~~~~~

../packages/sdk/src/transport/send/send.ts:6:22 - error TS2307: Cannot find module '@onflow/config' or its corresponding ty
pe declarations.

6 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.test.ts:1:22 - error TS2307: Cannot find module '@onflow/config' or i
ts corresponding type declarations.

1 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.test.ts:7:8 - error TS2307: Cannot find module '@onflow/typedefs' or 
its corresponding type declarations.

7 } from "@onflow/typedefs"
         ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.ts:1:22 - error TS2307: Cannot find module '@onflow/config' or its co
rresponding type declarations.

1 import {config} from "@onflow/config"
                       ~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.ts:2:47 - error TS2307: Cannot find module '@onflow/typedefs' or its 
corresponding type declarations.

2 import {SdkTransport, SubscriptionTopic} from "@onflow/typedefs"
                                                ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe-raw.ts:4:25 - error TS2307: Cannot find module '@onflow/util-invariant' o
r its corresponding type declarations.

4 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe.test.ts:6:8 - error TS2307: Cannot find module '@onflow/typedefs' or its 
corresponding type declarations.

6 } from "@onflow/typedefs"
         ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/subscribe.ts:1:61 - error TS2307: Cannot find module '@onflow/typedefs' or its corr
esponding type declarations.

1 import {SdkTransport, Subscription, SubscriptionTopic} from "@onflow/typedefs"
                                                              ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/transport/subscribe/types.ts:6:8 - error TS2307: Cannot find module '@onflow/typedefs' or its correspon
ding type declarations.

6 } from "@onflow/typedefs"
         ~~~~~~~~~~~~~~~~~~

../packages/sdk/src/wallet-utils/encode-signable.ts:1:38 - error TS2307: Cannot find module '@onflow/util-address' or its c
orresponding type declarations.

1 import {withPrefix, sansPrefix} from "@onflow/util-address"
                                       ~~~~~~~~~~~~~~~~~~~~~~

../packages/sdk/src/wallet-utils/validate-tx.ts:5:25 - error TS2307: Cannot find module '@onflow/util-invariant' or its cor
responding type declarations.

5 import {invariant} from "@onflow/util-invariant"
                          ~~~~~~~~~~~~~~~~~~~~~~~~


Found 106 errors in 55 files.

Errors  Files
     2  ../packages/sdk/src/account/account.ts:1
     2  ../packages/sdk/src/block/block.ts:1
     1  ../packages/sdk/src/build/build-arguments.ts:2
     1  ../packages/sdk/src/build/build-at-block-height.ts:2
     1  ../packages/sdk/src/build/build-at-block-id.ts:9
     1  ../packages/sdk/src/build/build-at-latest-block.ts:2
     1  ../packages/sdk/src/build/build-authorizations.ts:1
     1  ../packages/sdk/src/build/build-get-account.test.ts:2
     1  ../packages/sdk/src/build/build-get-account.ts:1
     1  ../packages/sdk/src/build/build-get-latest-block.ts:1
     1  ../packages/sdk/src/build/build-payer.test.ts:8
     1  ../packages/sdk/src/build/build-payer.ts:1
     1  ../packages/sdk/src/build/build-proposer.ts:1
     1  ../packages/sdk/src/build/build-script.ts:7
     2  ../packages/sdk/src/build/build-subscribe-events.ts:1
     1  ../packages/sdk/src/build/build-transaction.ts:8
     1  ../packages/sdk/src/build/build.ts:6
     1  ../packages/sdk/src/contract.test.ts:6
     1  ../packages/sdk/src/decode/decode-stream.test.ts:3
     1  ../packages/sdk/src/decode/decode-stream.ts:2
     1  ../packages/sdk/src/decode/decode.ts:1
     1  ../packages/sdk/src/decode/sdk-decode.ts:1
     2  ../packages/sdk/src/encode/encode.ts:2
     1  ../packages/sdk/src/interaction/interaction.test.ts:1
     4  ../packages/sdk/src/interaction/interaction.ts:1
     1  ../packages/sdk/src/node-version-info/node-version-info.ts:1
     4  ../packages/sdk/src/resolve/resolve-accounts.ts:1
     2  ../packages/sdk/src/resolve/resolve-arguments.ts:1
     1  ../packages/sdk/src/resolve/resolve-cadence.test.ts:8
     5  ../packages/sdk/src/resolve/resolve-cadence.ts:2
     1  ../packages/sdk/src/resolve/resolve-compute-limit.test.ts:6
     3  ../packages/sdk/src/resolve/resolve-compute-limit.ts:2
     2  ../packages/sdk/src/resolve/resolve-final-normalization.ts:1
     5  ../packages/sdk/src/resolve/resolve-proposer-sequence-number.ts:4
     4  ../packages/sdk/src/resolve/resolve-ref-block-id.ts:9
     2  ../packages/sdk/src/resolve/resolve-signatures.test.ts:12
     2  ../packages/sdk/src/resolve/resolve-signatures.ts:2
     1  ../packages/sdk/src/resolve/resolve-validators.ts:2
     1  ../packages/sdk/src/resolve/resolve-voucher-intercept.test.ts:1
     1  ../packages/sdk/src/resolve/resolve-voucher-intercept.ts:2
     9  ../packages/sdk/src/resolve/resolve.ts:2
     2  ../packages/sdk/src/resolve/voucher.ts:1
     6  ../packages/sdk/src/sdk.ts:1
     2  ../packages/sdk/src/test-utils/authz-fn.ts:1
     1  ../packages/sdk/src/test-utils/run.ts:4
     3  ../packages/sdk/src/transport/get-transport.test.ts:1
     4  ../packages/sdk/src/transport/get-transport.ts:1
     2  ../packages/sdk/src/transport/send/send.ts:1
     2  ../packages/sdk/src/transport/subscribe/subscribe-raw.test.ts:1
     3  ../packages/sdk/src/transport/subscribe/subscribe-raw.ts:1
     1  ../packages/sdk/src/transport/subscribe/subscribe.test.ts:6
     1  ../packages/sdk/src/transport/subscribe/subscribe.ts:1
     1  ../packages/sdk/src/transport/subscribe/types.ts:6
     1  ../packages/sdk/src/wallet-utils/encode-signable.ts:1
     1  ../packages/sdk/src/wallet-utils/validate-tx.ts:5

```

---



让我检查复制的依赖包结构：

---

<details>
            <summary>Listed directory pkg/node_modules/@onflow • **6** results</summary>
        
| Name |
|-------|
| 📁 `util-address` |
| 📁 `typedefs` |
| 📁 `transport-http` |
| 📁 `sdk` |
| 📁 `fcl-bundle` |
| 📁 `config` |

</details>

---

现在我需要检查这些包是否有 src 目录，并且确保 TypeScript 可以找到正确的类型定义：

---

<details>
            <summary>Listed directory pkg/node_modules/@onflow/typedefs • **1** results</summary>
        
| Name |
|-------|
| 📁 `node_modules` |

</details>

---

看起来复制的包中没有 src 目录。让我检查原始的 packages 目录结构：

---

<details>
            <summary>Listed directory packages/typedefs • **1** results</summary>
        
| Name |
|-------|
| 📁 `node_modules` |

</details>

---

让我检查这些包是否是 npm 包，看看有没有 package.json：

---



---



---

