#!/usr/bin/env node

// Example usage of the ESM SDK
import * as sdk from './dist/sdk.js';

console.log('🚀 Flow SDK ESM Example');
console.log('📦 SDK Version:', sdk.VERSION);

// Test basic functionality
try {
  // Create a simple script interaction
  const scriptInteraction = sdk.script`
    pub fun main(): String {
      return "Hello from Flow!"
    }
  `;
  
  console.log('✅ Script builder works');
  
  // Test argument building
  const args = sdk.args([
    sdk.arg("test", sdk.t.String)
  ]);
  
  console.log('✅ Argument builder works');
  
  // Test interaction building
  const interaction = sdk.build([
    scriptInteraction,
    args
  ]);
  
  console.log('✅ Interaction builder works');
  console.log('🎉 All basic functionality tests passed!');
  
} catch (error) {
  console.error('❌ Error:', error.message);
}
