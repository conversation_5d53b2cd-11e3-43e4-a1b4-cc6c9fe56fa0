#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Fix VERSION.js file
const versionFile = path.join(__dirname, 'es', 'VERSION.js');
if (fs.existsSync(versionFile)) {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const versionContent = `export const VERSION = "${packageJson.version}";\n`;
  fs.writeFileSync(versionFile, versionContent);
  console.log('✅ Fixed VERSION.js');
} else {
  console.log('⚠️  VERSION.js not found');
}
