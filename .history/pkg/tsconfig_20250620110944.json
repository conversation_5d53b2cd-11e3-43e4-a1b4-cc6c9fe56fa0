{
  "include": ["../packages/sdk/src/**/*"],
  "exclude": ["../packages/sdk/src/**/*.test.ts", "../packages/sdk/src/**/*.test.js"],
  "compilerOptions": {
    "rootDir": "../packages/sdk/src",
    "outDir": "./es",
    // "declarationDir": "./types",
    "module": "ES2020", // ESNext
    "target": "ES2020",
    "moduleResolution": "Node",
    "allowImportingTsExtensions": false,
    "declaration": true,
    "emitDeclarationOnly": false,
    "allowJs": true,
    "strict": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "lib": ["ES2020", "WebWorker", "DOM", "ES2022.Error"],
    "types": ["node"],
    "baseUrl": ".",
    "paths": {
      "@onflow/*": ["./node_modules/@onflow/*"]
    }
  }
}
